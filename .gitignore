# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Machine Learning / Data Science specific
# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.model

# Data files (large datasets)
*.csv
*.tsv
*.json
*.parquet
*.feather
*.xlsx
*.xls

# AutoGluon models
**/autogluon_models/

# Trained models directory
models/
checkpoints/

# Visualization outputs
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.pdf

# Logs
logs/
*.log

# IDE specific files
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
.\#*

# System specific files
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes
.fseventsd

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Project specific
# Configuration files with sensitive data
config.ini
settings.local.py
secrets.json

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache directories
.cache/
cache/

# Output directories
output/
results/
reports/*.png
reports/*.pdf

# API keys and credentials
.env.local
.env.development
.env.test
.env.production
api_keys.txt
credentials.json

# Large data files (comment out if you want to track some data)
data/*/feature.csv
data/*/features_enhanced*.csv
data/raw/
data/processed/
data/external/

# Experiment outputs
experiments/output/
experiments/logs/

# Documentation build
docs/_build/
docs/site/

AutogluonModels/