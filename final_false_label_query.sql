SET odps.sql.type.system.odps2=true;
SET odps.sql.datetime.type=timestamp;

-- Step 1: 获取宕机事件中的所有实例（用于排除）
WITH failure_instances AS (
    SELECT DISTINCT vm_name
    FROM ecs_dw.dw_cloudops_event_vm_survive_duration_all
    WHERE ds = MAX_PT('ecs_dw.dw_cloudops_event_vm_survive_duration_all')
      AND tp >= '2025-07-01 00:00:00'
      AND tp <= '2025-07-01 23:59:59'
      AND is_test_user = 'false'
      AND is_gamma = 'false'
),
-- Step 2: 选择活跃的非宕机实例
-- 从monitor_exception_sls_alert中选择2025-07-01当天有异常记录的实例
-- 排除掉发生宕机的实例
active_non_failure_instances AS (
    SELECT
        instanceid AS vm_name,
        ncip AS nc_ip,
        -- 定义模拟宕机时刻：选择该实例最后一次异常时间作为tp
        MAX(exceptiontime) AS simulated_tp
    FROM ecs_dw.monitor_exception_sls_alert
    WHERE ds = '********'  -- 只看7月1日的数据
      AND instanceid IS NOT NULL
      AND exceptiontime >= '2025-07-01 00:00:00'
      AND exceptiontime <= '2025-07-01 23:59:59'
      AND istestaccount = 'False'
      AND is_gamma_machine = 0
    GROUP BY instanceid, ncip
    -- 排除宕机实例
    HAVING instanceid NOT IN (SELECT vm_name FROM failure_instances)
    -- 随机采样：使用RAND()进行随机排序
    ORDER BY RAND()
    LIMIT 2000  -- 根据需要调整，控制false label的数量
),
-- Step 3: 为这些实例生成"模拟宕机事件"记录
simulated_failure_events AS (
    SELECT
        vm_name,
        simulated_tp AS tp,
        -- 分配false label：survive_duration > 4
        100 AS survive_duration,  -- 给一个大于4的值作为false label
        NULL AS notify_duration,
        NULL AS failure_down_duration,
        'simulated_no_failure' AS rule_name,
        'simulated_no_failure' AS reason,
        nc_ip
    FROM active_non_failure_instances
),
-- Step 4: 获取匹配的异常日志数据（与原始查询相同逻辑）
matched_alerts AS (
    -- 分支1：匹配虚拟机自身异常（instanceid = vm_name）
    SELECT
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.exceptiontime,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.tag_name
    FROM simulated_failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert
        WHERE ds >= '20250629' AND ds <= '********'  -- 包含7月1日前2天的数据
    ) a
    ON a.instanceid = f.vm_name
    WHERE
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp

    UNION ALL

    -- 分支2：匹配宿主机异常（ncip = nc_ip）
    SELECT
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.exceptiontime,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.tag_name
    FROM simulated_failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert
        WHERE ds >= '20250629' AND ds <= '********'  -- 包含7月1日前2天的数据
    ) a
    ON a.ncip = f.nc_ip
    WHERE
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp
),
-- Step 5: 去重处理（与原始查询相同）
deduplicated_alerts AS (
    SELECT
        vm_name,
        rule_name,
        nc_ip,
        tp,
        exceptiontime,
        exceptionname,
        exceptiontype,
        warninglevel,
        tag_name,
        survive_duration,
        ROW_NUMBER() OVER (
            PARTITION BY vm_name, rule_name, nc_ip, tp,
                         exceptionname, exceptiontype, warninglevel, tag_name
            ORDER BY exceptiontime ASC
        ) AS rn
    FROM matched_alerts
)
-- Step 6: 最终聚合结果
SELECT
    -- 分组键
    vm_name,
    rule_name,
    nc_ip,
    tp,

    -- 特征列表：去重后的异常记录
    COLLECT_LIST(
        STRUCT(
            exceptiontime,
            exceptionname,
            exceptiontype,
            warninglevel,
            tag_name
        )
    ) AS feature_list,

    -- 标签：对于false label，所有记录的survive_duration都是100
    ARRAY(100) AS label_list,

    -- 分组统计信息
    COUNT(*) AS group_size,

    -- 标记这是false label
    'false_label' AS sample_type

FROM deduplicated_alerts
WHERE rn = 1  -- 只保留每个异常组的第一个记录
GROUP BY vm_name, rule_name, nc_ip, tp
ORDER BY vm_name, tp;


