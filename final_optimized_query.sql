SET odps.sql.type.system.odps2=true;
SET odps.sql.datetime.type=timestamp;

-- Step 1: 提取 2025-07-01 的所有宕机事件
WITH failure_events AS (
    SELECT
        vm_name,
        tp,
        survive_duration,
        notify_duration,
        failure_down_duration,
        rule_name,
        reason,
        nc_ip
    FROM    ecs_dw.dw_cloudops_event_vm_survive_duration_all
    WHERE   ds = MAX_PT('ecs_dw.dw_cloudops_event_vm_survive_duration_all')
      AND   tp >= '2025-07-01 00:00:00'
      AND   tp <= '2025-07-01 23:59:59'
      AND   is_test_user = 'false'
      AND   is_gamma = 'false'
),
-- Step 2: 获取匹配的异常日志数据
matched_alerts AS (
    -- 分支1：匹配虚拟机自身异常（instanceid = vm_name）
    SELECT
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.exceptiontime,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.tag_name
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert
        WHERE ds >= '20250629' AND ds <= '20250701'  -- 包含7月1日前2天的数据
    ) a
    ON a.instanceid = f.vm_name
    WHERE
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp

    UNION ALL

    -- 分支2：匹配宿主机异常（ncip = nc_ip）
    SELECT
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.exceptiontime,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.tag_name
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert
        WHERE ds >= '20250629' AND ds <= '20250701'  -- 包含7月1日前2天的数据
    ) a
    ON a.ncip = f.nc_ip
    WHERE
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp
),
-- Step 3: 分组聚合，模拟Python预处理逻辑（带去重）
deduplicated_alerts AS (
    SELECT
        vm_name,
        rule_name,
        nc_ip,
        tp,
        exceptiontime,
        exceptionname,
        exceptiontype,
        warninglevel,
        tag_name,
        survive_duration,
        -- 使用ROW_NUMBER按时间排序，对相同异常保留最早的记录
        ROW_NUMBER() OVER (
            PARTITION BY vm_name, rule_name, nc_ip, tp,
                         exceptionname, exceptiontype, warninglevel, tag_name
            ORDER BY exceptiontime ASC
        ) AS rn
    FROM matched_alerts
)
SELECT
    -- 分组键
    vm_name,
    rule_name,
    nc_ip,
    tp,

    -- 特征列表：去重后的异常记录（相同异常保留时间最早的）
    COLLECT_LIST(
        STRUCT(
            exceptiontime,
            exceptionname,
            exceptiontype,
            warninglevel,
            tag_name
        )
    ) AS feature_list,

    -- 标签处理：检查survive_duration是否在分组内都相同
    CASE
        WHEN COUNT(DISTINCT survive_duration) = 1 THEN
            -- 所有值都相同，返回单个值
            ARRAY(MAX(survive_duration))
        ELSE
            -- 存在不同值，返回所有值并标记告警
            COLLECT_LIST(survive_duration)
    END AS label_list,

    -- 分组统计信息
    COUNT(*) AS group_size,

    -- 标签一致性检查结果
    CASE
        WHEN COUNT(DISTINCT survive_duration) = 1 THEN 'consistent'
        ELSE 'warning_different_labels'
    END AS label_status,

    -- 唯一标签值数量
    COUNT(DISTINCT survive_duration) AS distinct_label_count,

    -- 其他统计信息
    MIN(survive_duration) AS min_survive_duration,
    MAX(survive_duration) AS max_survive_duration,
    AVG(survive_duration) AS avg_survive_duration

FROM deduplicated_alerts
WHERE rn = 1  -- 只保留每个异常组的第一个（时间最早的）记录
GROUP BY vm_name, rule_name, nc_ip, tp
ORDER BY vm_name, tp;
