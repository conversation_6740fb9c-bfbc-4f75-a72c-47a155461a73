# %%

import os
import sys
from typing import List, Dict, Any
import json
import numpy as np
import pandas as pd
from collections import Counter
import warnings

warnings.filterwarnings("ignore")

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.nn.utils.rnn import pad_sequence
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns

# Import rich for progress bars (environment guaranteed to have rich)
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn, MofNCompleteColumn
from rich.console import Console
from rich.table import Table
from rich import print as rprint

# Try to import experiment tracking libraries
try:
    import swanlab

    SWANLAB_AVAILABLE = True
    print("SwanLab available for experiment tracking")
except ImportError:
    SWANLAB_AVAILABLE = False
    print("SwanLab not available. Install with: pip install swanlab")

BASE_PATH = os.path.abspath(os.path.join(__file__, "../../.."))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

# Import functions from original sequence_modeling.py
from control_log_diagnose.src2.sequence_modeling import load_sequences_from_clustered, compute_global_failure_stats, analyze_failed_sequence, _to_epoch_seconds


# Set device with macOS Metal support and multi-GPU detection
def get_device():
    if torch.cuda.is_available():
        return torch.device("cuda")
    elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
        return torch.device("mps")
    else:
        return torch.device("cpu")


def get_device_count():
    if torch.cuda.is_available():
        return torch.cuda.device_count()
    else:
        return 0


device = get_device()
device_count = get_device_count()
print(f"Using device: {device}")
if device_count > 1:
    print(f"Detected {device_count} GPUs, will use DistributedDataParallel for training")


class SequenceTokenizer:
    """
    Tokenizer to convert cluster_id sequences into token sequences
    """

    def __init__(self, sequences: List[List[str]], max_vocab_size: int = 5000):
        self.max_vocab_size = max_vocab_size
        self.pad_token = "[PAD]"
        self.cls_token = "[CLS]"
        self.sep_token = "[SEP]"
        self.unk_token = "[UNK]"

        # Count frequency of all cluster_ids
        cluster_counter = Counter()
        for seq in sequences:
            cluster_counter.update(seq)

        # Build vocabulary: special tokens + high-frequency cluster_ids
        special_tokens = [self.pad_token, self.cls_token, self.sep_token, self.unk_token]
        most_common_clusters = [item[0] for item in cluster_counter.most_common(max_vocab_size - len(special_tokens))]

        self.vocab = special_tokens + most_common_clusters
        self.token_to_id = {token: idx for idx, token in enumerate(self.vocab)}
        self.id_to_token = {idx: token for token, idx in self.token_to_id.items()}

        # Special token IDs
        self.pad_token_id = self.token_to_id[self.pad_token]
        self.cls_token_id = self.token_to_id[self.cls_token]
        self.sep_token_id = self.token_to_id[self.sep_token]
        self.unk_token_id = self.token_to_id[self.unk_token]

        print(f"Vocabulary size: {len(self.vocab)}")
        print(f"Most common clusters: {most_common_clusters[:10]}")

    def encode(self, sequence: List[str], max_length: int = 512) -> List[int]:
        """
        Encode sequence into token ID list
        Format: [CLS] + sequence + [SEP]
        """
        # Convert cluster_id to token_id
        token_ids = [self.cls_token_id]
        for cluster_id in sequence:
            token_id = self.token_to_id.get(cluster_id, self.unk_token_id)
            token_ids.append(token_id)
        token_ids.append(self.sep_token_id)

        # Truncate or pad to specified length
        if len(token_ids) > max_length:
            token_ids = token_ids[: max_length - 1] + [self.sep_token_id]

        return token_ids

    def decode(self, token_ids: List[int]) -> List[str]:
        """Decode token ID list back to sequence"""
        return [self.id_to_token.get(tid, self.unk_token) for tid in token_ids]


class SequenceDataset(Dataset):
    """
    Sequence classification dataset
    """

    def __init__(self, sequences: List[List[str]], labels: List[int], tokenizer: SequenceTokenizer, max_length: int = 512):
        self.sequences = sequences
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]

        # Encode sequence
        token_ids = self.tokenizer.encode(sequence, max_length=self.max_length)

        return {
            "input_ids": torch.tensor(token_ids, dtype=torch.long),
            "attention_mask": torch.ones(len(token_ids), dtype=torch.long),
            "labels": torch.tensor(label, dtype=torch.long),
            "sequence_length": torch.tensor(len(token_ids), dtype=torch.long),
        }


def collate_fn(batch):
    """
    Batch processing function to handle sequences of different lengths
    """
    input_ids = [item["input_ids"] for item in batch]
    labels = [item["labels"] for item in batch]
    sequence_lengths = [item["sequence_length"] for item in batch]

    # Pad sequences to same length
    input_ids_padded = pad_sequence(input_ids, batch_first=True, padding_value=0)  # 0 is pad_token_id

    # Create attention mask
    attention_mask = torch.zeros_like(input_ids_padded)
    for i, length in enumerate(sequence_lengths):
        attention_mask[i, :length] = 1

    return {"input_ids": input_ids_padded, "attention_mask": attention_mask, "labels": torch.stack(labels), "sequence_lengths": torch.stack(sequence_lengths)}


class TransformerSequenceClassifier(nn.Module):
    """
    Transformer-based sequence classification model
    """

    def __init__(
        self, vocab_size: int, d_model: int = 256, nhead: int = 8, num_layers: int = 6, num_classes: int = 2, max_length: int = 512, dropout: float = 0.1
    ):
        super().__init__()
        self.d_model = d_model
        self.max_length = max_length

        # Embedding layers
        self.token_embedding = nn.Embedding(vocab_size, d_model, padding_idx=0)
        self.position_embedding = nn.Embedding(max_length, d_model)

        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead, dim_feedforward=d_model * 4, dropout=dropout, batch_first=True)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout), nn.Linear(d_model, d_model // 2), nn.ReLU(), nn.Dropout(dropout), nn.Linear(d_model // 2, num_classes)
        )

        self.dropout = nn.Dropout(dropout)

    def forward(self, input_ids, attention_mask=None, return_attention=False):
        batch_size, seq_len = input_ids.shape

        # Token embedding + Position embedding
        positions = torch.arange(seq_len, device=input_ids.device).unsqueeze(0).expand(batch_size, -1)
        embeddings = self.token_embedding(input_ids) + self.position_embedding(positions)
        embeddings = self.dropout(embeddings)

        # Create padding mask (Transformer expects inverted mask)
        if attention_mask is not None:
            # attention_mask: 1 for real tokens, 0 for padding
            # src_key_padding_mask: True for padding, False for real tokens
            src_key_padding_mask = attention_mask == 0
        else:
            src_key_padding_mask = None

        # Transformer encoding
        if return_attention:
            # Modify transformer to return attention weights
            encoder_outputs = []
            attention_weights = []
            x = embeddings

            for layer in self.transformer.layers:
                # Manually compute attention for each layer
                x_norm = layer.norm1(x)
                attn_output, attn_weights = layer.self_attn(
                    x_norm,
                    x_norm,
                    x_norm,
                    key_padding_mask=src_key_padding_mask,
                    need_weights=True,
                    average_attn_weights=False,  # Return attention from all heads
                )
                x = x + layer.dropout1(attn_output)

                # Feed forward
                x_norm2 = layer.norm2(x)
                ff_output = layer.linear2(layer.dropout(layer.activation(layer.linear1(x_norm2))))
                x = x + layer.dropout2(ff_output)

                encoder_outputs.append(x)
                attention_weights.append(attn_weights)  # shape: [batch, num_heads, seq_len, seq_len]

            encoded = x
        else:
            encoded = self.transformer(embeddings, src_key_padding_mask=src_key_padding_mask)
            attention_weights = None

        # Use [CLS] token (first position) for classification
        cls_output = encoded[:, 0, :]  # [batch_size, d_model]
        logits = self.classifier(cls_output)

        if return_attention:
            return logits, attention_weights
        else:
            return logits


def train_transformer_classifier(
    ds: pd.DataFrame,
    test_size: float = 0.2,
    random_state: int = 42,
    max_length: int = 512,
    batch_size: int = 16,
    learning_rate: float = 1e-4,
    num_epochs: int = 10,
    d_model: int = 256,
    nhead: int = 8,
    num_layers: int = 6,
    project_name: str = "sequence-transformer",
    experiment_name: str = None,
    enable_tracking: bool = True,
    use_kfold: bool = False,
    n_splits: int = 5,
) -> Dict[str, Any]:
    """
    Train Transformer sequence classifier with experiment tracking

    Args:
        ds: DataFrame with pattern sequences and labels
        test_size: Fraction of data to use for testing (when not using k-fold)
        random_state: Random seed for reproducibility
        max_length: Maximum sequence length
        batch_size: Training batch size
        learning_rate: Learning rate for optimizer
        num_epochs: Number of training epochs
        d_model: Model dimension
        nhead: Number of attention heads
        num_layers: Number of transformer layers
        project_name: Project name for experiment tracking
        experiment_name: Experiment name for tracking
        enable_tracking: Whether to enable experiment tracking
        use_kfold: Whether to use k-fold cross validation
        n_splits: Number of splits for k-fold cross validation
    """

    # Initialize experiment tracking
    tracker = None
    if enable_tracking:
        if SWANLAB_AVAILABLE:
            # SwanLab setup
            experiment_name = experiment_name or f"transformer_{d_model}d_{num_layers}l_{learning_rate}lr"
            tracker = swanlab.init(
                project=project_name,
                experiment_name=experiment_name,
                config={
                    "model_type": "transformer",
                    "d_model": d_model,
                    "nhead": nhead,
                    "num_layers": num_layers,
                    "max_length": max_length,
                    "batch_size": batch_size,
                    "learning_rate": learning_rate,
                    "num_epochs": num_epochs,
                    "test_size": test_size,
                    "random_state": random_state,
                },
            )
            print(f"SwanLab experiment initialized: {experiment_name}")
        else:
            print("No experiment tracking available. Training without logging.")

    # Prepare data
    sequences = ds["pattern_sequence"].tolist()
    labels = ds["label_mapped"].astype(int).tolist()
    indices = np.arange(len(labels))

    # Create tokenizer
    tokenizer = SequenceTokenizer(sequences, max_vocab_size=5000)

    # Log vocabulary size
    if tracker and SWANLAB_AVAILABLE:
        swanlab.log({"vocab_size": len(tokenizer.vocab)})

    # K-Fold Cross Validation
    if use_kfold:
        print(f"Using {n_splits}-fold cross validation")
        skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=random_state)

        fold_results = []
        fold_models = []

        for fold, (train_idx, val_idx) in enumerate(skf.split(sequences, labels)):
            print(f"\n=== Fold {fold + 1}/{n_splits} ===")

            # Split data for this fold
            train_seqs = [sequences[i] for i in train_idx]
            val_seqs = [sequences[i] for i in val_idx]
            train_labels = [labels[i] for i in train_idx]
            val_labels = [labels[i] for i in val_idx]

            # Create datasets
            train_dataset = SequenceDataset(train_seqs, train_labels, tokenizer, max_length)
            val_dataset = SequenceDataset(val_seqs, val_labels, tokenizer, max_length)

            # Create data loaders
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)

            # Create model
            model = TransformerSequenceClassifier(vocab_size=len(tokenizer.vocab), d_model=d_model, nhead=nhead, num_layers=num_layers, max_length=max_length)

            # Use DataParallel for multi-GPU training
            if device_count > 1:
                model = model.to(device)
                model = torch.nn.DataParallel(model)
            else:
                model = model.to(device)

            # Optimizer
            optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate)

            # Training loop for this fold
            model.train()
            train_losses = []

            print(f"Starting training for fold {fold + 1}...")

            # Create console for progress bars
            fold_console = Console()

            # Create progress bar for fold training
            with Progress(
                TextColumn(f"[bold blue]Fold {fold+1}", justify="right"),
                BarColumn(bar_width=None),
                "[progress.percentage]{task.percentage:>3.1f}%",
                "•",
                MofNCompleteColumn(),
                "•",
                TextColumn("Loss: {task.fields[loss]:.4f}"),
                "•",
                TextColumn("Epoch: {task.fields[epoch]}/{task.fields[total_epochs]}"),
                "•",
                TimeElapsedColumn(),
                "•",
                TimeRemainingColumn(),
                console=fold_console,
                expand=True,
            ) as fold_progress:

                # Create overall training progress task for this fold
                total_batches = len(train_loader) * num_epochs
                overall_task = fold_progress.add_task("Training", total=total_batches, loss=0.0, epoch=0, total_epochs=num_epochs)

                for epoch in range(num_epochs):
                    epoch_loss = 0
                    for batch_idx, batch in enumerate(train_loader):
                        # Move data to device
                        input_ids = batch["input_ids"].to(device)
                        attention_mask = batch["attention_mask"].to(device)
                        batch_labels = batch["labels"].to(device)

                        # Forward pass
                        optimizer.zero_grad()
                        logits = model(input_ids, attention_mask)
                        loss = F.cross_entropy(logits, batch_labels)

                        # Backward pass
                        loss.backward()
                        optimizer.step()

                        epoch_loss += loss.item()

                        # Update progress
                        fold_progress.update(overall_task, advance=1, loss=loss.item(), epoch=epoch + 1, total_epochs=num_epochs)

                        # Log to experiment tracker
                        if tracker and SWANLAB_AVAILABLE:
                            step = fold * num_epochs * len(train_loader) + epoch * len(train_loader) + batch_idx
                            log_data = {f"fold_{fold+1}/train_loss": loss.item(), f"fold_{fold+1}/epoch": epoch + 1}
                            swanlab.log(log_data, step=step)

                avg_loss = epoch_loss / len(train_loader)
                train_losses.append(avg_loss)

                if tracker and SWANLAB_AVAILABLE:
                    step = fold * num_epochs + epoch
                    log_data = {f"fold_{fold+1}/train_loss": avg_loss, f"fold_{fold+1}/epoch": epoch + 1}
                    swanlab.log(log_data, step=step)

                # Print epoch summary
                fold_console.print(f"[green]Fold {fold+1} - Epoch {epoch+1}/{num_epochs} completed[/green] - Average Loss: {avg_loss:.4f}")

            # Evaluate on validation set for this fold
            model.eval()
            all_preds = []
            all_labels = []

            # Create progress bar for fold evaluation
            with Progress(
                TextColumn(f"[bold green]Fold {fold+1} Eval", justify="right"),
                BarColumn(bar_width=None),
                "[progress.percentage]{task.percentage:>3.1f}%",
                "•",
                MofNCompleteColumn(),
                "•",
                TimeElapsedColumn(),
                console=fold_console,
                expand=True,
            ) as eval_progress:

                eval_task = eval_progress.add_task("Evaluating", total=len(val_loader))

                with torch.no_grad():
                    for batch in val_loader:
                        input_ids = batch["input_ids"].to(device)
                        attention_mask = batch["attention_mask"].to(device)
                        batch_labels = batch["labels"].to(device)

                        logits = model(input_ids, attention_mask)
                        preds = torch.argmax(logits, dim=-1)

                        all_preds.extend(preds.cpu().numpy())
                        all_labels.extend(batch_labels.cpu().numpy())

                        # Update progress
                        eval_progress.update(eval_task, advance=1)

            # Calculate metrics for this fold
            accuracy = accuracy_score(all_labels, all_preds)
            precision = precision_score(all_labels, all_preds, average="binary")
            recall = recall_score(all_labels, all_preds, average="binary")
            f1 = f1_score(all_labels, all_preds, average="binary")

            fold_result = {
                "fold": fold + 1,
                "accuracy": float(accuracy),
                "precision": float(precision),
                "recall": float(recall),
                "f1": float(f1),
                "train_losses": train_losses,
            }

            fold_results.append(fold_result)
            fold_models.append(model)

            if tracker and SWANLAB_AVAILABLE:
                eval_metrics = {
                    f"fold_{fold+1}/eval_accuracy": accuracy,
                    f"fold_{fold+1}/eval_precision": precision,
                    f"fold_{fold+1}/eval_recall": recall,
                    f"fold_{fold+1}/eval_f1": f1,
                }
                swanlab.log(eval_metrics)

            print(f"Fold {fold+1} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")

        # Calculate average metrics across all folds
        avg_accuracy = np.mean([r["accuracy"] for r in fold_results])
        avg_precision = np.mean([r["precision"] for r in fold_results])
        avg_recall = np.mean([r["recall"] for r in fold_results])
        avg_f1 = np.mean([r["f1"] for r in fold_results])

        print(f"\n=== Cross Validation Results ===")
        print(f"Average Accuracy: {avg_accuracy:.4f}")
        print(f"Average Precision: {avg_precision:.4f}")
        print(f"Average Recall: {avg_recall:.4f}")
        print(f"Average F1: {avg_f1:.4f}")

        # Use the best model (highest F1 score) as the final model
        best_fold_idx = np.argmax([r["f1"] for r in fold_results])
        best_model = fold_models[best_fold_idx]
        best_fold_result = fold_results[best_fold_idx]

        # For simplicity, we'll use the last fold's test set as the final test set in the returned dict
        # In practice, you might want to have a separate holdout test set
        train_seqs = [sequences[i] for i in train_idx]
        test_seqs = [sequences[i] for i in val_idx]
        train_labels = [labels[i] for i in train_idx]
        test_labels = [labels[i] for i in val_idx]
        train_idx = train_idx
        test_idx = val_idx

        train_dataset = SequenceDataset(train_seqs, train_labels, tokenizer, max_length)
        test_dataset = SequenceDataset(test_seqs, test_labels, tokenizer, max_length)

        # Return results
        return {
            "model": best_model,
            "tokenizer": tokenizer,
            "train_dataset": train_dataset,
            "test_dataset": test_dataset,
            "train_idx": train_idx,
            "test_idx": test_idx,
            "train_losses": best_fold_result["train_losses"],
            "accuracy": avg_accuracy,
            "precision": avg_precision,
            "recall": avg_recall,
            "f1": avg_f1,
            "fold_results": fold_results,
            "device": device,
            "tracker": tracker,
        }

    else:
        # Standard train/test split (original behavior)
        train_seqs, test_seqs, train_labels, test_labels, train_idx, test_idx = train_test_split(
            sequences, labels, indices, test_size=test_size, random_state=random_state, stratify=labels
        )

        # Create datasets
        train_dataset = SequenceDataset(train_seqs, train_labels, tokenizer, max_length)
        test_dataset = SequenceDataset(test_seqs, test_labels, tokenizer, max_length)

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)

        # Create model
        model = TransformerSequenceClassifier(vocab_size=len(tokenizer.vocab), d_model=d_model, nhead=nhead, num_layers=num_layers, max_length=max_length)

        # Use DataParallel for multi-GPU training
        if device_count > 1:
            model = model.to(device)
            model = torch.nn.DataParallel(model)
            print(f"Using {device_count} GPUs with DataParallel")
            print("Note: For better performance and scalability, consider using DistributedDataParallel (DDP) in production.")
        else:
            model = model.to(device)

        # Optimizer
        optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate)

    # Training loop
    model.train()
    train_losses = []

    print("Starting Transformer model training...")

    console = Console()

    with Progress(
        TextColumn("[bold blue]Training", justify="right"),
        BarColumn(bar_width=None),
        "[progress.percentage]{task.percentage:>3.1f}%",
        "•",
        MofNCompleteColumn(),
        "•",
        TextColumn("Loss: {task.fields[loss]:.4f}"),
        "•",
        TextColumn("Epoch: {task.fields[epoch]}/{task.fields[total_epochs]}"),
        "•",
        TimeElapsedColumn(),
        "•",
        TimeRemainingColumn(),
        console=console,
        expand=True,
    ) as progress:

        # Create overall training progress task
        total_batches = len(train_loader) * num_epochs
        overall_task = progress.add_task("Training", total=total_batches, loss=0.0, epoch=0, total_epochs=num_epochs)

        batch_counter = 0
        for epoch in range(num_epochs):
            epoch_loss = 0
            for batch_idx, batch in enumerate(train_loader):
                # Move data to device
                input_ids = batch["input_ids"].to(device)
                attention_mask = batch["attention_mask"].to(device)
                labels = batch["labels"].to(device)

                # Forward pass
                optimizer.zero_grad()
                # When using DataParallel, the forward pass should work directly
                logits = model(input_ids, attention_mask)
                loss = F.cross_entropy(logits, labels)

                # Backward pass
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()
                batch_counter += 1

                # Update progress
                progress.update(overall_task, advance=1, loss=loss.item(), epoch=epoch + 1, total_epochs=num_epochs)

                # Log to experiment tracker
                if tracker and SWANLAB_AVAILABLE:
                    step = epoch * len(train_loader) + batch_idx
                    log_data = {"train/loss": loss.item(), "train/epoch": epoch + 1}
                    swanlab.log(log_data, step=step)
            avg_loss = epoch_loss / len(train_loader)
            train_losses.append(avg_loss)

            # Log epoch metrics
            if tracker and SWANLAB_AVAILABLE:
                epoch_data = {"train/epoch_loss": avg_loss, "train/epoch": epoch + 1}
                swanlab.log(epoch_data, step=(epoch + 1) * len(train_loader))

            # Print epoch summary
            console.print(f"[green]Epoch {epoch+1}/{num_epochs} completed[/green] - Average Loss: {avg_loss:.4f}")

    # Evaluate model
    model.eval()
    all_preds = []
    all_labels = []

    print("\nEvaluating model on test set...")

    with Progress(
        TextColumn("[bold green]Evaluation", justify="right"),
        BarColumn(bar_width=None),
        "[progress.percentage]{task.percentage:>3.1f}%",
        "•",
        MofNCompleteColumn(),
        "•",
        TextColumn("Accuracy: {task.fields[accuracy]:.3f}"),
        "•",
        TimeElapsedColumn(),
        console=console,
        expand=True,
    ) as progress:

        eval_task = progress.add_task("Evaluating", total=len(test_loader), accuracy=0.0)

        correct_predictions = 0
        total_predictions = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(test_loader):
                input_ids = batch["input_ids"].to(device)
                attention_mask = batch["attention_mask"].to(device)
                labels = batch["labels"].to(device)

                # When using DataParallel, the forward pass should work directly
                logits = model(input_ids, attention_mask)
                preds = torch.argmax(logits, dim=-1)

                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

                # Calculate running accuracy
                correct_predictions += (preds == labels).sum().item()
                total_predictions += labels.size(0)
                current_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0

                # Update progress
                progress.update(eval_task, advance=1, accuracy=current_accuracy)

    # Calculate metrics
    accuracy = accuracy_score(all_labels, all_preds)
    precision = precision_score(all_labels, all_preds, average="binary")
    recall = recall_score(all_labels, all_preds, average="binary")
    f1 = f1_score(all_labels, all_preds, average="binary")
    report = classification_report(all_labels, all_preds, output_dict=True)
    cm = confusion_matrix(all_labels, all_preds).tolist()

    # Log final evaluation metrics
    if tracker and SWANLAB_AVAILABLE:
        eval_metrics = {"eval/accuracy": accuracy, "eval/precision": precision, "eval/recall": recall, "eval/f1": f1}
        swanlab.log(eval_metrics)

    # Display results
    console = Console()
    # Create a results table
    table = Table(title="Model Evaluation Results")
    table.add_column("Metric", style="cyan", no_wrap=True)
    table.add_column("Value", style="magenta")

    table.add_row("Accuracy", f"{accuracy:.4f}")
    table.add_row("Precision", f"{precision:.4f}")
    table.add_row("Recall", f"{recall:.4f}")
    table.add_row("F1 Score", f"{f1:.4f}")

    console.print(table)

    # Finish experiment tracking
    if tracker and SWANLAB_AVAILABLE:
        swanlab.finish()
        print("SwanLab experiment completed")

    return {
        "model": model,
        "tokenizer": tokenizer,
        "train_dataset": train_dataset,
        "test_dataset": test_dataset,
        "train_idx": train_idx,
        "test_idx": test_idx,
        "train_losses": train_losses,
        "accuracy": float(accuracy),
        "precision": float(precision),
        "recall": float(recall),
        "f1": float(f1),
        "report": report,
        "confusion_matrix": cm,
        "device": device,
        "tracker": tracker,
    }


def explain_failed_with_attention(
    seq: List[str],
    model: TransformerSequenceClassifier,
    tokenizer: SequenceTokenizer,
    top_n: int = 5,
    original_df: pd.DataFrame = None,
    key_type: str = None,
    key_values: Dict[str, Any] = None,
    max_length: int = 512,
) -> Dict[str, Any]:
    """
    Explain failed sequence using attention weights
    """
    model.eval()

    # Encode sequence
    token_ids = tokenizer.encode(seq, max_length=max_length)
    input_ids = torch.tensor([token_ids], dtype=torch.long).to(device)
    attention_mask = torch.ones_like(input_ids)

    with torch.no_grad():
        # Get predictions and attention weights
        # When using DataParallel, we need to handle the forward pass differently
        if hasattr(model, "module"):
            # Model is wrapped with DataParallel
            logits, attention_weights = model.module(input_ids, attention_mask, return_attention=True)
        else:
            # Model is not wrapped
            logits, attention_weights = model(input_ids, attention_mask, return_attention=True)
        prediction = torch.softmax(logits, dim=-1)[0]  # [num_classes]
        failure_prob = prediction[1].item()  # Failure probability

    # Process attention weights
    # attention_weights: List[Tensor], each Tensor shape: [batch, num_heads, seq_len, seq_len]

    # Calculate average attention weights (across layers and heads)
    all_attention = torch.stack(attention_weights)  # [num_layers, batch, num_heads, seq_len, seq_len]
    mean_attention = all_attention.mean(dim=(0, 2)).squeeze(0)  # [seq_len, seq_len]

    # Get attention from [CLS] token to other positions (for classification)
    cls_attention = mean_attention[0, 1:-1]  # Exclude [CLS] and [SEP] positions

    # Decode tokens
    decoded_tokens = tokenizer.decode(token_ids)
    sequence_tokens = decoded_tokens[1:-1]  # Exclude [CLS] and [SEP]

    # Find key nodes (based on attention weights)
    if len(cls_attention) > 0 and len(sequence_tokens) > 0:
        # Ensure length matching
        min_len = min(len(cls_attention), len(sequence_tokens))
        cls_attention = cls_attention[:min_len]
        sequence_tokens = sequence_tokens[:min_len]

        # Node importance
        node_importance = []
        for i, (token, attention_weight) in enumerate(zip(sequence_tokens, cls_attention)):
            if token not in [tokenizer.pad_token, tokenizer.cls_token, tokenizer.sep_token, tokenizer.unk_token]:
                node_importance.append({"node": token, "attention_weight": float(attention_weight), "position": i})

        # Sort by attention weight
        node_importance.sort(key=lambda x: x["attention_weight"], reverse=True)

        # Deduplicate by node ID, keeping the one with highest attention weight
        node_dict = {}
        for node_info in node_importance:
            node_id = node_info["node"]
            # Keep the one with highest attention weight
            if node_id not in node_dict or node_dict[node_id]["attention_weight"] < node_info["attention_weight"]:
                node_dict[node_id] = node_info

        # Convert to list and sort by attention weight again
        deduplicated_nodes = sorted(node_dict.values(), key=lambda x: x["attention_weight"], reverse=True)
        top_nodes = deduplicated_nodes[:top_n]

        # Transition importance (adjacent token pairs' attention)
        transition_importance = []
        for i in range(len(sequence_tokens) - 1):
            if i < len(cls_attention) - 1:
                token_a = sequence_tokens[i]
                token_b = sequence_tokens[i + 1]
                if token_a not in [tokenizer.pad_token, tokenizer.cls_token, tokenizer.sep_token, tokenizer.unk_token] and token_b not in [
                    tokenizer.pad_token,
                    tokenizer.cls_token,
                    tokenizer.sep_token,
                    tokenizer.unk_token,
                ]:

                    # Use average of adjacent positions' attention as transition importance
                    transition_weight = (cls_attention[i] + cls_attention[i + 1]) / 2
                    transition_importance.append({"transition": f"{token_a} -> {token_b}", "attention_weight": float(transition_weight), "position": i})

        transition_importance.sort(key=lambda x: x["attention_weight"], reverse=True)
        top_transitions = transition_importance[:top_n]
    else:
        top_nodes = []
        top_transitions = []

    result = {
        "failure_probability": float(failure_prob),
        "top_nodes": top_nodes,
        "top_transitions": top_transitions,
        "sequence_length": len(seq),
        "attention_summary": {
            "mean_attention": float(cls_attention.mean()) if len(cls_attention) > 0 else 0.0,
            "max_attention": float(cls_attention.max()) if len(cls_attention) > 0 else 0.0,
            "min_attention": float(cls_attention.min()) if len(cls_attention) > 0 else 0.0,
        },
    }

    # Attach raw logs (robust matching with fallbacks)
    if original_df is not None and key_type and key_values:
        try:
            df = original_df.copy()
            df["__time_sec"] = df["__time__"].apply(_to_epoch_seconds)
            df["action_ts"] = pd.to_datetime(df["action_time"], errors="coerce").astype("int64") / 1e9
            df = df[~df["__time_sec"].isna() & ~df["action_ts"].isna()].copy()
            df = df[df["__time_sec"] <= df["action_ts"]].copy()

            # Normalize types for robust equality
            if "request_id" in df.columns:
                df["request_id"] = df["request_id"].astype(str)
            if "action_time" in df.columns:
                df["action_time"] = df["action_time"].astype(str)
            if "instance_id" in df.columns:
                df["instance_id"] = df["instance_id"].astype(str)

            # Primary filtering by key
            if key_type == "request_id" and "request_id" in df.columns:
                req_val = str(key_values.get("request_id", ""))
                df_req = df[df["request_id"] == req_val].sort_values("__time_sec")
            elif key_type == "action_instance" and {"action_time", "instance_id"}.issubset(df.columns):
                at_val = str(key_values.get("action_time", ""))
                inst_val = str(key_values.get("instance_id", ""))
                df_req = df[(df["action_time"] == at_val) & (df["instance_id"] == inst_val)].sort_values("__time_sec")
            else:
                df_req = pd.DataFrame()

            # Raw logs list (empty if not found)
            logs = []
            if not df_req.empty:
                for _, r in df_req.iterrows():
                    logs.append(
                        {
                            "__time__": str(r.get("__time__", "")),
                            "action_time": str(r.get("action_time", "")),
                            "cluster_id": str(r.get("cluster_id", "")),
                            "content": str(r.get("content", "")).replace("\n", " ")[:1200],
                        }
                    )
            result["raw_logs"] = logs

            # Choose a source for approximate matching if strict filter empty
            df_source = df_req if not df_req.empty else df

            # Always attempt to attach FROM/TO for top transitions (default None)
            cluster_seq = df_source["cluster_id"].astype(str).tolist() if (not df_source.empty and "cluster_id" in df_source.columns) else []
            for titem in result.get("top_transitions", []):
                # Default
                titem["from_log"], titem["to_log"] = None, None
                trans_str = titem.get("transition", "")
                parts = trans_str.split(" -> ")
                if len(parts) == 2 and cluster_seq:
                    a, b = parts[0], parts[1]
                    found = False
                    for idx in range(len(cluster_seq) - 1):
                        if cluster_seq[idx] == a and cluster_seq[idx + 1] == b:
                            r_a = df_source.iloc[idx]
                            r_b = df_source.iloc[idx + 1]
                            titem["from_log"] = {
                                "idx": int(idx),
                                "__time__": str(r_a.get("__time__", "")),
                                "action_time": str(r_a.get("action_time", "")),
                                "cluster_id": str(r_a.get("cluster_id", "")),
                                "content": str(r_a.get("content", "")).replace("\n", " ")[:1200],
                            }
                            titem["to_log"] = {
                                "idx": int(idx + 1),
                                "__time__": str(r_b.get("__time__", "")),
                                "action_time": str(r_b.get("action_time", "")),
                                "cluster_id": str(r_b.get("cluster_id", "")),
                                "content": str(r_b.get("content", "")).replace("\n", " ")[:1200],
                            }
                            found = True
                            break

            # Attach example logs for top nodes (up to 3 entries, deduplicated by content)
            for nitem in result.get("top_nodes", []):
                node_id = str(nitem.get("node", ""))
                node_logs = []
                if node_id and not df_source.empty and "cluster_id" in df_source.columns:
                    sub = df_source[df_source["cluster_id"].astype(str) == node_id]
                    # Deduplicate by content and take head
                    if not sub.empty:
                        # Remove duplicates based on content
                        sub_dedup = sub.drop_duplicates(subset=["content"], keep="first")
                        # Take first 3 entries
                        sub_head = sub_dedup.head(3)
                        for _, r in sub_head.iterrows():
                            node_logs.append(
                                {
                                    "__time__": str(r.get("__time__", "")),
                                    "action_time": str(r.get("action_time", "")),
                                    "cluster_id": str(r.get("cluster_id", "")),
                                    "content": str(r.get("content", "")).replace("\n", " "),
                                }
                            )
                nitem["examples"] = node_logs

        except Exception as e:
            result["raw_logs_error"] = str(e)

    return result


def save_transformer_eval_report(output_dir: str, eval_result: Dict[str, Any], model_name: str = "transformer"):
    """Save Transformer model evaluation report"""
    os.makedirs(output_dir, exist_ok=True)
    report_path = os.path.join(output_dir, f"{model_name}_sequence_classifier_eval.json")

    # Check if this is k-fold results
    if "fold_results" in eval_result:
        # K-fold cross validation results
        fold_results = eval_result.get("fold_results", [])
        save_data = {
            "model_type": "transformer",
            "kfold_cross_validation": True,
            "num_folds": len(fold_results),
            "fold_results": fold_results,
            "average_accuracy": eval_result.get("accuracy"),
            "average_precision": eval_result.get("precision"),
            "average_recall": eval_result.get("recall"),
            "average_f1": eval_result.get("f1"),
            "training_info": {
                "vocab_size": len(eval_result.get("tokenizer").vocab) if eval_result.get("tokenizer") else None,
            },
        }
    else:
        # Standard train/test split results
        save_data = {
            "model_type": "transformer",
            "kfold_cross_validation": False,
            "accuracy": eval_result.get("accuracy"),
            "precision": eval_result.get("precision"),
            "recall": eval_result.get("recall"),
            "f1": eval_result.get("f1"),
            "classification_report": eval_result.get("report"),
            "confusion_matrix": eval_result.get("confusion_matrix"),
            "training_info": {
                "vocab_size": len(eval_result.get("tokenizer").vocab) if eval_result.get("tokenizer") else None,
                "final_loss": eval_result.get("train_losses", [])[-1] if eval_result.get("train_losses") else None,
            },
        }

    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(save_data, f, ensure_ascii=False, indent=2)

    print(f"Transformer classification evaluation report saved: {report_path}")


def train_and_save_model():
    """Train Transformer model and save it to disk"""
    clustered_dir = os.path.join(BASE_PATH, "output", "control_log_diagnose", "clustered_data")
    artifacts_dir = os.path.join(BASE_PATH, "control_log_diagnose", "artifacts")
    os.makedirs(artifacts_dir, exist_ok=True)

    print("Loading and building sequence dataset...")
    ds = load_sequences_from_clustered(clustered_dir)
    print(f"Total sequences: {len(ds)} | Label distribution: {ds['label_mapped'].value_counts().to_dict()}")

    print("Training Transformer classification model...")
    eval_transformer = train_transformer_classifier(
        ds,
        test_size=0.2,
        batch_size=256,
        learning_rate=1e-4,
        num_epochs=5,
        d_model=256,
        nhead=8,
        num_layers=4,
        project_name="log-sequence-analysis",
        experiment_name="transformer_baseline",
        enable_tracking=True,
        use_kfold=True,
        n_splits=5,
    )

    print(f"Transformer Acc: {eval_transformer['accuracy']:.4f}")
    print(f"Transformer F1: {eval_transformer['f1']:.4f}")

    # Save evaluation report
    save_transformer_eval_report(artifacts_dir, eval_transformer)

    # Save trained model
    model_save_dir = os.path.join(BASE_PATH, "output", "control_log_diagnose", "models")
    save_transformer_model(eval_transformer["model"], eval_transformer["tokenizer"], model_save_dir, "sequence_transformer")

    # Load model and perform inference
    load_model_and_inference()

    return eval_transformer


def save_transformer_model(model, tokenizer, output_dir: str, model_name: str = "transformer"):
    """Save the trained Transformer model and tokenizer"""
    os.makedirs(output_dir, exist_ok=True)

    # Get the actual model (unwrap from DataParallel if needed)
    if hasattr(model, "module"):
        # Model is wrapped with DataParallel/DDP
        actual_model = model.module
    else:
        # Model is not wrapped
        actual_model = model

    # Save model state dict
    model_path = os.path.join(output_dir, f"{model_name}_model.pth")
    torch.save(actual_model.state_dict(), model_path)

    # Save tokenizer vocabulary
    tokenizer_path = os.path.join(output_dir, f"{model_name}_tokenizer.json")
    tokenizer_data = {
        "vocab": tokenizer.vocab,
        "token_to_id": tokenizer.token_to_id,
        "id_to_token": tokenizer.id_to_token,
        "pad_token_id": tokenizer.pad_token_id,
        "cls_token_id": tokenizer.cls_token_id,
        "sep_token_id": tokenizer.sep_token_id,
        "unk_token_id": tokenizer.unk_token_id,
        "max_vocab_size": getattr(tokenizer, "max_vocab_size", 5000),
    }

    with open(tokenizer_path, "w", encoding="utf-8") as f:
        json.dump(tokenizer_data, f, ensure_ascii=False, indent=2)

    print(f"Transformer model saved: {model_path}")
    print(f"Tokenizer saved: {tokenizer_path}")


def load_transformer_model(
    model_path: str, tokenizer_path: str, d_model: int = 256, nhead: int = 8, num_layers: int = 6, max_length: int = 512, num_classes: int = 2
):
    """Load a trained Transformer model and tokenizer from saved files"""
    # Load tokenizer
    with open(tokenizer_path, "r", encoding="utf-8") as f:
        tokenizer_data = json.load(f)

    # Recreate tokenizer
    tokenizer = SequenceTokenizer([], max_vocab_size=tokenizer_data.get("max_vocab_size", 5000))
    tokenizer.vocab = tokenizer_data["vocab"]
    tokenizer.token_to_id = (
        {str(k): int(v) for k, v in tokenizer_data["token_to_id"].items()}
        if isinstance(tokenizer_data.get("token_to_id"), dict)
        else tokenizer_data["token_to_id"]
    )
    # id_to_token keys were serialized to strings in JSON; convert back to ints
    tokenizer.id_to_token = {int(k): v for k, v in tokenizer_data["id_to_token"].items()}
    tokenizer.pad_token_id = (
        int(tokenizer_data["pad_token_id"]) if isinstance(tokenizer_data.get("pad_token_id"), (int, str)) else tokenizer_data["pad_token_id"]
    )
    tokenizer.cls_token_id = (
        int(tokenizer_data["cls_token_id"]) if isinstance(tokenizer_data.get("cls_token_id"), (int, str)) else tokenizer_data["cls_token_id"]
    )
    tokenizer.sep_token_id = (
        int(tokenizer_data["sep_token_id"]) if isinstance(tokenizer_data.get("sep_token_id"), (int, str)) else tokenizer_data["sep_token_id"]
    )
    tokenizer.unk_token_id = (
        int(tokenizer_data["unk_token_id"]) if isinstance(tokenizer_data.get("unk_token_id"), (int, str)) else tokenizer_data["unk_token_id"]
    )

    # Create model
    model = TransformerSequenceClassifier(
        vocab_size=len(tokenizer.vocab), d_model=d_model, nhead=nhead, num_layers=num_layers, num_classes=num_classes, max_length=max_length
    )

    # Load model state dict
    model.load_state_dict(torch.load(model_path, map_location=device))

    # Apply DataParallel if multiple GPUs are available
    if device_count > 1:
        model = model.to(device)
        model = torch.nn.DataParallel(model)
        print(f"Using {device_count} GPUs with DataParallel")
    else:
        model = model.to(device)

    print(f"Transformer model loaded from {model_path}")
    print(f"Tokenizer loaded from {tokenizer_path}")

    return model, tokenizer


def load_model_and_inference():
    """Load trained model and perform inference on failed samples"""
    # Load the model and tokenizer from disk
    model_save_dir = os.path.join(BASE_PATH, "output", "control_log_diagnose", "models")
    model_path = os.path.join(model_save_dir, "sequence_transformer_model.pth")
    tokenizer_path = os.path.join(model_save_dir, "sequence_transformer_tokenizer.json")

    loaded_model, loaded_tokenizer = load_transformer_model(model_path, tokenizer_path, d_model=256, nhead=8, num_layers=4)

    # Load dataset for analysis
    clustered_dir = os.path.join(BASE_PATH, "output", "control_log_diagnose", "clustered_data")
    artifacts_dir = os.path.join(BASE_PATH, "control_log_diagnose", "artifacts")
    os.makedirs(artifacts_dir, exist_ok=True)

    ds = load_sequences_from_clustered(clustered_dir)

    # Calculate global statistics (for comparison)
    print("Computing global failure statistics (for comparison)...")
    global_stats = compute_global_failure_stats(ds)
    print(f"Transition stats: {len(global_stats['trans_stats'])} | Node stats: {len(global_stats['node_stats'])}")

    # Initialize a separate tracker for explanation/analysis phase
    analysis_tracker = None
    if SWANLAB_AVAILABLE:
        try:
            analysis_tracker = swanlab.init(
                project="log-sequence-analysis",
                experiment_name="transformer_explain",
                config={
                    "stage": "explain",
                    "model_type": "transformer",
                },
            )
            print("SwanLab analysis run initialized: transformer_explain")
        except Exception as e:
            print(f"Failed to initialize SwanLab for analysis: {e}")

    # Demonstrate failed sample explanation
    # Sample failed samples using the same logic as sequence_modeling.py
    # Sample 10 failed samples from each file
    failed_samples = ds[ds["label_mapped"] == 1].groupby("file").head(10).reset_index(drop=True)
    if not failed_samples.empty:
        all_explanations = []

        print("\nAnalyzing failed samples...")

        # Cache original dataframes by file to avoid repeated IO operations
        file_cache = {}

        with Progress(
            TextColumn("[bold cyan]Analyzing", justify="right"),
            BarColumn(bar_width=None),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            MofNCompleteColumn(),
            "•",
            TextColumn("Sample: {task.fields[sample_info]}"),
            "•",
            TimeElapsedColumn(),
            console=Console(),
            expand=True,
        ) as progress:

            analysis_task = progress.add_task("Samples", total=len(failed_samples), sample_info="Loading...")

            for idx, (_, row) in enumerate(failed_samples.iterrows()):
                seq = row["pattern_sequence"]
                sample_info = f"{idx+1}/{len(failed_samples)} (len:{len(seq)})"

                progress.update(analysis_task, sample_info=sample_info)

                # Load original data with caching to avoid repeated IO operations
                src_file = row.get("file")
                orig_df = None
                try:
                    if src_file in file_cache:
                        # Use cached dataframe
                        orig_df = file_cache[src_file]
                    else:
                        # Load and cache dataframe
                        fpath = os.path.join(BASE_PATH, "output", "control_log_diagnose", "clustered_data", src_file)
                        orig_df = pd.read_parquet(fpath, engine="pyarrow")
                        file_cache[src_file] = orig_df
                except Exception:
                    pass

                # Statistical method explanation
                explain_stat = analyze_failed_sequence(seq, global_stats, top_n=5)
                
                # Attach raw logs to stat analysis results
                if orig_df is not None and row.get("key_type") and row.get("key_values"):
                    try:
                        # Add raw logs to stat analysis
                        df = orig_df.copy()
                        df["__time_sec"] = df["__time__"].apply(_to_epoch_seconds)
                        df["action_ts"] = pd.to_datetime(df["action_time"], errors="coerce").astype("int64") / 1e9
                        df = df[~df["__time_sec"].isna() & ~df["action_ts"].isna()].copy()
                        df = df[df["__time_sec"] <= df["action_ts"]].copy()
                        
                        # Primary filtering by key
                        if row.get("key_type") == "request_id" and "request_id" in df.columns:
                            req_val = str(row.get("key_values", {}).get("request_id", ""))
                            df_req = df[df["request_id"] == req_val].sort_values("__time_sec")
                        elif row.get("key_type") == "action_instance" and {"action_time", "instance_id"}.issubset(df.columns):
                            at_val = str(row.get("key_values", {}).get("action_time", ""))
                            inst_val = str(row.get("key_values", {}).get("instance_id", ""))
                            df_req = df[(df["action_time"] == at_val) & (df["instance_id"] == inst_val)].sort_values("__time_sec")
                        else:
                            df_req = pd.DataFrame()
                        
                        # Raw logs list (empty if not found)
                        logs = []
                        if not df_req.empty:
                            for _, r in df_req.iterrows():
                                logs.append(
                                    {
                                        "__time__": str(r.get("__time__", "")),
                                        "action_time": str(r.get("action_time", "")),
                                        "cluster_id": str(r.get("cluster_id", "")),
                                        "content": str(r.get("content", "")).replace("\n", " ")[:1200],
                                    }
                                )
                        # explain_stat["raw_logs"] = logs
                        
                        # Attach example logs for top nodes in stat analysis
                        cluster_seq = df_req["cluster_id"].astype(str).tolist() if (not df_req.empty and "cluster_id" in df_req.columns) else []
                        for nitem in explain_stat.get("top_nodes", []):
                            node_id = str(nitem.get("node", ""))
                            node_logs = []
                            if node_id and not df_req.empty and "cluster_id" in df_req.columns:
                                sub = df_req[df_req["cluster_id"].astype(str) == node_id]
                                # Deduplicate by content and take head
                                if not sub.empty:
                                    # Remove duplicates based on content
                                    sub_dedup = sub.drop_duplicates(subset=["content"], keep="first")
                                    # Take first 3 entries
                                    sub_head = sub_dedup.head(3)
                                    for _, r in sub_head.iterrows():
                                        node_logs.append(
                                            {
                                                "__time__": str(r.get("__time__", "")),
                                                "action_time": str(r.get("action_time", "")),
                                                "cluster_id": str(r.get("cluster_id", "")),
                                                "content": str(r.get("content", "")).replace("\n", " "),
                                            }
                                        )
                            nitem["examples"] = node_logs
                            
                        # Attach FROM/TO logs for top transitions in stat analysis
                        for titem in explain_stat.get("top_transitions", []):
                            # Default
                            titem["from_log"], titem["to_log"] = None, None
                            trans_str = titem.get("transition", "")
                            parts = trans_str.split(" -> ")
                            if len(parts) == 2 and cluster_seq:
                                a, b = parts[0], parts[1]
                                found = False
                                for idx in range(len(cluster_seq) - 1):
                                    if cluster_seq[idx] == a and cluster_seq[idx + 1] == b:
                                        r_a = df_req.iloc[idx]
                                        r_b = df_req.iloc[idx + 1]
                                        titem["from_log"] = {
                                            "idx": int(idx),
                                            "__time__": str(r_a.get("__time__", "")),
                                            "action_time": str(r_a.get("action_time", "")),
                                            "cluster_id": str(r_a.get("cluster_id", "")),
                                            "content": str(r_a.get("content", "")).replace("\n", " ")[:1200],
                                        }
                                        titem["to_log"] = {
                                            "idx": int(idx + 1),
                                            "__time__": str(r_b.get("__time__", "")),
                                            "action_time": str(r_b.get("action_time", "")),
                                            "cluster_id": str(r_b.get("cluster_id", "")),
                                            "content": str(r_b.get("content", "")).replace("\n", " ")[:1200],
                                        }
                                        found = True
                                        break
                    except Exception as e:
                        explain_stat["raw_logs_error"] = str(e)
                
                # Transformer attention explanation
                explain_transformer = explain_failed_with_attention(
                    seq,
                    loaded_model,  # 使用加载的模型而不是训练后的模型
                    loaded_tokenizer,  # 使用加载的分词器而不是训练后的分词器
                    top_n=5,
                    original_df=orig_df,
                    key_type=row.get("key_type"),
                    key_values=row.get("key_values"),
                )

                explanation = {
                    "sample_meta": {
                        "file": src_file,
                        "key_type": row.get("key_type"),
                        "key_values": row.get("key_values"),
                        "sequence_length": len(seq),
                        "sequence": seq,
                    },
                    "stat_analysis": explain_stat,
                    "transformer_analysis": explain_transformer,
                }

                all_explanations.append(explanation)

                # Log explanation metrics to experiment tracker
                if analysis_tracker and SWANLAB_AVAILABLE:
                    metrics = {
                        "explain/sequence_length": len(seq),
                        "explain/failure_probability": explain_transformer.get("failure_probability", 0.0),
                        "explain/attention/mean": explain_transformer.get("attention_summary", {}).get("mean_attention", 0.0),
                        "explain/attention/max": explain_transformer.get("attention_summary", {}).get("max_attention", 0.0),
                    }
                    # Top node weights (up to 3)
                    for i, node in enumerate(explain_transformer.get("top_nodes", [])[:3], start=1):
                        if isinstance(node, dict) and "attention_weight" in node:
                            metrics[f"explain/top_nodes/{i}/weight"] = float(node["attention_weight"])
                    # Top transition weights (up to 3)
                    for i, trans in enumerate(explain_transformer.get("top_transitions", [])[:3], start=1):
                        if isinstance(trans, dict) and "attention_weight" in trans:
                            metrics[f"explain/top_transitions/{i}/weight"] = float(trans["attention_weight"])
                    swanlab.log(metrics, step=idx)

                # Record top nodes and top transitions to swanlab tables (deduplicated by node)
                if SWANLAB_AVAILABLE and explain_transformer.get("top_nodes") and explain_transformer.get("top_transitions"):
                    # Create deduplicated top nodes table
                    node_dict = {}  # Use dict to deduplicate by node ID
                    for node in explain_transformer["top_nodes"]:
                        if isinstance(node, dict) and "node" in node and "attention_weight" in node:
                            node_id = node["node"]
                            # Keep the one with highest attention weight
                            if node_id not in node_dict or node_dict[node_id]["attention_weight"] < node["attention_weight"]:
                                # Concatenate all example contents with double newlines
                                example_contents = []
                                if node.get("examples"):
                                    for example in node["examples"]:
                                        if example.get("content"):
                                            example_contents.append(example["content"])
                                example_content_str = "\n\n".join(example_contents)

                                node_dict[node_id] = {
                                    "node_id": node_id,
                                    "attention_weight": node["attention_weight"],
                                    "position": node.get("position", -1),
                                    "example_content": example_content_str,
                                }

                    # Convert to list and sort by attention weight
                    deduped_nodes = sorted(node_dict.values(), key=lambda x: x["attention_weight"], reverse=True)[:10]

                    # Create swanlab table for top nodes
                    if deduped_nodes:
                        node_headers = ["File", "Node ID", "Attention Weight", "Position", "Example Content"]
                        # Get file name from sample_meta
                        file_name = explanation.get("sample_meta", {}).get("file", "Unknown")
                        node_rows = [
                            [file_name, item["node_id"], item["attention_weight"], item["position"], item["example_content"]] for item in deduped_nodes
                        ]

                        node_table = swanlab.echarts.Table()

                        node_table.add(node_headers, node_rows)
                        swanlab.log({f"top_nodes_sample": node_table}, step=idx)

                    # Create top transitions table
                    transition_headers = ["File", "Transition", "Attention Weight", "Position", "From Content", "To Content"]
                    # Get file name from sample_meta
                    file_name = explanation.get("sample_meta", {}).get("file", "Unknown")
                    transition_rows = []
                    for trans in explain_transformer["top_transitions"][:10]:  # Top 10 transitions
                        if isinstance(trans, dict) and "transition" in trans and "attention_weight" in trans:
                            from_content = trans.get("from_log", {}).get("content", "") if trans.get("from_log") else ""
                            to_content = trans.get("to_log", {}).get("content", "") if trans.get("to_log") else ""
                            transition_rows.append(
                                [file_name, trans["transition"], trans["attention_weight"], trans.get("position", -1), from_content, to_content]
                            )

                    if transition_rows:
                        trans_table = swanlab.echarts.Table()

                        trans_table.add(transition_headers, transition_rows)
                        swanlab.log({f"top_transitions_sample": trans_table}, step=idx)
                
                # Record stat_analysis top nodes and top transitions to swanlab tables (deduplicated by node)
                if SWANLAB_AVAILABLE and explain_stat.get("top_nodes") and explain_stat.get("top_transitions"):
                    # Create deduplicated top nodes table for stat analysis
                    stat_node_dict = {}  # Use dict to deduplicate by node ID
                    for node in explain_stat["top_nodes"]:
                        if isinstance(node, dict) and "node" in node:
                            node_id = node["node"]
                            # Keep the one with highest relative risk
                            if node_id not in stat_node_dict or stat_node_dict[node_id]["relative_risk"] < node.get("relative_risk", 0):
                                # Concatenate all example contents with double newlines
                                example_contents = []
                                if node.get("examples"):
                                    for example in node["examples"]:
                                        if example.get("content"):
                                            example_contents.append(example["content"])
                                example_content_str = "\n\n".join(example_contents)
                                
                                stat_node_dict[node_id] = {
                                    "node_id": node_id,
                                    "relative_risk": node.get("relative_risk", 0),
                                    "failure_rate": node.get("failure_rate", 0),
                                    "position": node.get("position", -1),
                                    "example_content": example_content_str,
                                }
                    
                    # Convert to list and sort by relative risk
                    stat_deduped_nodes = sorted(stat_node_dict.values(), key=lambda x: x["relative_risk"], reverse=True)[:10]
                    
                    # Create swanlab table for stat top nodes
                    if stat_deduped_nodes:
                        stat_node_headers = ["File", "Node ID", "Relative Risk", "Failure Rate", "Position", "Example Content"]
                        # Get file name from sample_meta
                        file_name = explanation.get("sample_meta", {}).get("file", "Unknown")
                        stat_node_rows = [
                            [file_name, item["node_id"], item["relative_risk"], item["failure_rate"], item["position"], item["example_content"]] for item in stat_deduped_nodes
                        ]
                        
                        stat_node_table = swanlab.echarts.Table()
                        
                        stat_node_table.add(stat_node_headers, stat_node_rows)
                        swanlab.log({f"stat_top_nodes_sample": stat_node_table}, step=idx)
                    
                    # Create stat top transitions table
                    stat_transition_headers = ["File", "Transition", "Relative Risk", "Failure Rate", "Position", "From Content", "To Content"]
                    # Get file name from sample_meta
                    file_name = explanation.get("sample_meta", {}).get("file", "Unknown")
                    stat_transition_rows = []
                    for trans in explain_stat["top_transitions"][:10]:  # Top 10 transitions
                        if isinstance(trans, dict) and "transition" in trans:
                            from_content = trans.get("from_log", {}).get("content", "") if trans.get("from_log") else ""
                            to_content = trans.get("to_log", {}).get("content", "") if trans.get("to_log") else ""
                            stat_transition_rows.append(
                                [file_name, trans["transition"], trans.get("relative_risk", 0), trans.get("failure_rate", 0), trans.get("position", -1), from_content, to_content]
                            )
                    
                    if stat_transition_rows:
                        stat_trans_table = swanlab.echarts.Table()
                        
                        stat_trans_table.add(stat_transition_headers, stat_transition_rows)
                        swanlab.log({f"stat_top_transitions_sample": stat_trans_table}, step=idx)

                # Create analysis results table
                analysis_table = Table(title=f"Failed Sample {idx+1} Analysis")
                analysis_table.add_column("Metric", style="cyan")
                analysis_table.add_column("Value", style="yellow")

                analysis_table.add_row("Sequence Length", str(len(seq)))
                analysis_table.add_row("Failure Probability", f"{explain_transformer['failure_probability']:.4f}")

                # Top nodes
                if explain_transformer["top_nodes"]:
                    for i, node_info in enumerate(explain_transformer["top_nodes"][:3]):
                        analysis_table.add_row(f"Top Node {i+1}", f"{node_info['node']}: {node_info['attention_weight']:.4f}")

                # Top transitions
                if explain_transformer["top_transitions"]:
                    for i, trans_info in enumerate(explain_transformer["top_transitions"][:3]):
                        analysis_table.add_row(f"Top Transition {i+1}", f"{trans_info['transition']}: {trans_info['attention_weight']:.4f}")

                Console().print(analysis_table)

                progress.update(analysis_task, advance=1)

        # Save all explanation results
        explain_path = os.path.join(artifacts_dir, "transformer_failed_cases_explanations.json")
        with open(explain_path, "w", encoding="utf-8") as f:
            json.dump(all_explanations, f, ensure_ascii=False, indent=2)

        print(f"\nTransformer failed sample explanations saved: {explain_path}")

        # Finish analysis tracker
        if analysis_tracker and SWANLAB_AVAILABLE:
            swanlab.finish()

    print("Transformer sequence analysis completed!")

    return loaded_model, loaded_tokenizer


# %% Main execution function
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Transformer Sequence Classifier")
    parser.add_argument(
        "--mode",
        choices=["train", "inference"],
        default="inference",
        help="Execution mode: 'train' to train and save model, 'inference' to load model and analyze failed samples",
    )

    args = parser.parse_args()

    if args.mode == "train":
        # Train model and save it
        print("Starting training mode...")
        train_and_save_model()
        print("Training completed and model saved!")
    elif args.mode == "inference":
        # Load model and perform inference
        print("Starting inference mode...")
        load_model_and_inference()
        print("Inference completed!")
    else:
        print("Invalid mode. Use 'train' or 'inference'")

# %%
