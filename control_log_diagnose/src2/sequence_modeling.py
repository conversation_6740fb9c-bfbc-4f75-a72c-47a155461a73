# %%

import os
import sys
from typing import List, Tu<PERSON>, Dict, Any
import json
import numpy as np
import pandas as pd
from collections import Counter, defaultdict

from sklearn.model_selection import train_test_split
from sklearn.feature_extraction import DictVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
from sklearn.metrics import precision_score, recall_score, f1_score

BASE_PATH = os.path.abspath(os.path.join(__file__, "../../.."))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)


def _to_epoch_seconds(x):
    if pd.isna(x):
        return np.nan
    try:
        val = float(x)
        if val > 1e12:
            return val / 1000.0
        return val
    except Exception:
        try:
            return pd.to_datetime(x, errors="coerce").timestamp()
        except Exception:
            return np.nan


def load_sequences_from_clustered(
    clustered_dir: str,
    require_failed: bool = True,
) -> pd.DataFrame:
    """
    从 output/control_log_diagnose/clustered_data 逐文件读取构建序列数据集。

    Returns: DataFrame[file, request_id, label_mapped(0/1), pattern_sequence(list[str])]
    """
    if not os.path.exists(clustered_dir):
        raise FileNotFoundError(f"Not found: {clustered_dir}")

    parquet_files = sorted([f for f in os.listdir(clustered_dir) if f.endswith(".parquet")])
    if not parquet_files:
        raise RuntimeError("No parquet files found in clustered_dir")

    rows: List[Dict[str, Any]] = []
    for fname in parquet_files:
        fpath = os.path.join(clustered_dir, fname)
        try:
            df = pd.read_parquet(fpath, engine="pyarrow")
        except Exception as e:
            print(f"skip {fname}: {e}")
            continue

        # 过滤必须字段
        required_cols = ["__time__", "action_time", "action_type", "cluster_id"]
        if any(c not in df.columns for c in required_cols):
            print(f"[{fname}] missing columns -> skip")
            continue

        # 时间解析与筛选
        df["__time_sec"] = df["__time__"].apply(_to_epoch_seconds)
        df["action_ts"] = pd.to_datetime(df["action_time"], errors="coerce").astype("int64") / 1e9
        df = df[~df["__time_sec"].isna() & ~df["action_ts"].isna()].copy()

        # 只保留 start_vm_success/failed
        df = df[df["action_type"].isin(["start_vm_success", "start_vm_failed"])].copy()
        if df.empty:
            continue
        if require_failed and (df["action_type"] == "start_vm_failed").sum() == 0:
            # 如果没有失败样本，可按需跳过该文件
            print(f"[{fname}] no failed cases -> skip by option")
            continue

        # 过滤窗口（<= action_time）——与序列构建脚本保持一致
        df = df[df["__time_sec"] <= df["action_ts"]].copy()
        if df.empty:
            continue

        # 分组键：优先 request_id，其次 (action_time, instance_id)
        if "request_id" in df.columns:
            group_key = "request_id"
        elif "instance_id" in df.columns and "action_time" in df.columns:
            group_key = ["action_time", "instance_id"]
        else:
            print(f"[{fname}] no request key -> skip")
            continue

        grouped = df.groupby(group_key)
        for rid, g in grouped:
            if g.empty:
                continue
            has_failed = (g["action_type"] == "start_vm_failed").any()
            label_mapped = 1 if has_failed else 0
            g_sorted = g.sort_values("__time_sec")
            seq = g_sorted["cluster_id"].astype(str).tolist()
            if not seq:
                continue
            # 保存分组键结构，便于后续检索原始日志
            if isinstance(group_key, str):
                key_type = "request_id"
                key_values = {"request_id": rid}
            else:
                # rid 为元组 (action_time, instance_id)
                key_type = "action_instance"
                try:
                    action_time_val, instance_id_val = rid
                except Exception:
                    action_time_val, instance_id_val = None, None
                key_values = {"action_time": action_time_val, "instance_id": instance_id_val}

            rows.append(
                {
                    "file": fname,
                    "request_id": rid if isinstance(rid, str) else str(rid),
                    "key_type": key_type,
                    "key_values": key_values,
                    "label_mapped": label_mapped,
                    "pattern_sequence": seq,
                }
            )

    ds = pd.DataFrame(rows)
    if ds.empty:
        raise RuntimeError("Empty dataset built from clustered_dir")
    return ds


def extract_features(seqs: List[List[str]]) -> Tuple[np.ndarray, DictVectorizer, List[Dict[str, float]]]:
    """
    提取序列特征（字典特征）
    - 节点计数（unigram）
    - 转换计数（2-gram）
    - 基础统计特征（长度、多样性等）
    """
    feat_dicts: List[Dict[str, float]] = []
    for seq in seqs:
        d: Dict[str, float] = {}
        # 节点
        for p, c in Counter(seq).items():
            d[f"node::{p}"] = float(c)
        # 2-gram
        for i in range(len(seq) - 1):
            t = f"trans::{seq[i]}->{seq[i+1]}"
            d[t] = d.get(t, 0.0) + 1.0
        # 基础特征
        length = len(seq)
        uniq = len(set(seq))
        d["stat::length"] = float(length)
        d["stat::uniq"] = float(uniq)
        d["stat::diversity"] = float(uniq / length) if length > 0 else 0.0
        feat_dicts.append(d)

    vec = DictVectorizer(sparse=True)
    X = vec.fit_transform(feat_dicts)
    return X, vec, feat_dicts


def extract_features_with_vec(seqs: List[List[str]], vec: DictVectorizer) -> Tuple[np.ndarray, List[Dict[str, float]]]:
    feat_dicts: List[Dict[str, float]] = []
    for seq in seqs:
        d: Dict[str, float] = {}
        for p, c in Counter(seq).items():
            d[f"node::{p}"] = float(c)
        for i in range(len(seq) - 1):
            t = f"trans::{seq[i]}->{seq[i+1]}"
            d[t] = d.get(t, 0.0) + 1.0
        length = len(seq)
        uniq = len(set(seq))
        d["stat::length"] = float(length)
        d["stat::uniq"] = float(uniq)
        d["stat::diversity"] = float(uniq / length) if length > 0 else 0.0
        feat_dicts.append(d)
    X = vec.transform(feat_dicts)
    return X, feat_dicts


def train_sequence_classifier(ds: pd.DataFrame, test_size: float = 0.2, random_state: int = 42) -> Dict[str, Any]:
    """
    训练模型1：序列成功/失败分类器
    返回模型、向量器、评估指标与划分数据
    """
    X_all, vec, _ = extract_features(ds["pattern_sequence"].tolist())
    y_all = ds["label_mapped"].astype(int).values

    X_train, X_test, y_train, y_test, train_idx, test_idx = train_test_split(
        X_all, y_all, np.arange(len(y_all)), test_size=test_size, random_state=random_state, stratify=y_all
    )

    clf = LogisticRegression(max_iter=1000, n_jobs=None)
    clf.fit(X_train, y_train)

    y_pred = clf.predict(X_test)
    acc = accuracy_score(y_test, y_pred)
    report = classification_report(y_test, y_pred, output_dict=True)
    cm = confusion_matrix(y_test, y_pred).tolist()

    # 计算 Precision, Recall, F1
    precision = precision_score(y_test, y_pred, average="binary")
    recall = recall_score(y_test, y_pred, average="binary")
    f1 = f1_score(y_test, y_pred, average="binary")

    return {
        "model": clf,
        "vectorizer": vec,
        "X_train": X_train,
        "X_test": X_test,
        "y_train": y_train,
        "y_test": y_test,
        "train_idx": train_idx,
        "test_idx": test_idx,
        "accuracy": float(acc),
        "precision": float(precision),
        "recall": float(recall),
        "f1": float(f1),
        "report": report,
        "confusion_matrix": cm,
    }


def train_xgb_classifier(ds: pd.DataFrame, test_size: float = 0.2, random_state: int = 42) -> Dict[str, Any]:
    """
    学习能力更强的模型（XGBoost）用于分类，同时为模型2提供基于TreeSHAP的特征归因。
    """
    X_all, vec, _ = extract_features(ds["pattern_sequence"].tolist())
    y_all = ds["label_mapped"].astype(int).values

    X_train, X_test, y_train, y_test, train_idx, test_idx = train_test_split(
        X_all, y_all, np.arange(len(y_all)), test_size=test_size, random_state=random_state, stratify=y_all
    )

    clf = xgb.XGBClassifier(
        n_estimators=300,
        max_depth=6,
        learning_rate=0.05,
        subsample=0.9,
        colsample_bytree=0.9,
        reg_lambda=1.0,
        objective="binary:logistic",
        tree_method="hist",
        random_state=random_state,
        n_jobs=4,
    )
    clf.fit(X_train, y_train)

    y_pred = clf.predict(X_test)
    acc = accuracy_score(y_test, y_pred)
    report = classification_report(y_test, y_pred, output_dict=True)
    cm = confusion_matrix(y_test, y_pred).tolist()

    return {
        "model": clf,
        "vectorizer": vec,
        "feature_names": vec.get_feature_names_out().tolist(),
        "X_train": X_train,
        "X_test": X_test,
        "y_train": y_train,
        "y_test": y_test,
        "train_idx": train_idx,
        "test_idx": test_idx,
        "accuracy": float(acc),
        "report": report,
        "confusion_matrix": cm,
    }


def explain_failed_with_xgb(
    seq: List[str],
    model: xgb.XGBClassifier,
    vec: DictVectorizer,
    top_n: int = 5,
    original_df: pd.DataFrame = None,
    key_type: str = None,
    key_values: Dict[str, Any] = None,
) -> Dict[str, Any]:
    """
    使用XGBoost的TreeSHAP（pred_contribs）解释单个失败序列，输出TopN关键转换/节点特征及贡献。
    """
    X_case, feat_dicts = extract_features_with_vec([seq], vec)
    # 使用Booster + pred_contribs获取特征贡献
    booster = model.get_booster()
    dm = xgb.DMatrix(X_case)
    contribs = booster.predict(dm, pred_contribs=True)
    contrib = contribs[0]  # 包含bias在最后一维
    feature_names = vec.get_feature_names_out()

    feat_contrib = []
    for i, name in enumerate(feature_names):
        feat_contrib.append((name, float(contrib[i])))
    # 仅保留在该序列中非零的特征（出现过的节点/转换/统计）
    nonzero_feature_mask = np.array(X_case.todense())[0] > 0
    feat_contrib = [(feature_names[i], float(contrib[i])) for i in range(len(feature_names)) if nonzero_feature_mask[i]]

    # 拆分节点/转换特征，按正向贡献（推高失败概率）排序
    node_items = [(n, v) for n, v in feat_contrib if n.startswith("node::")]
    trans_items = [(n, v) for n, v in feat_contrib if n.startswith("trans::")]
    node_items.sort(key=lambda x: x[1], reverse=True)
    trans_items.sort(key=lambda x: x[1], reverse=True)

    top_nodes = [{"node": n.split("node::", 1)[1], "contribution": float(v)} for n, v in node_items[:top_n]]
    top_transitions = []
    for n, v in trans_items[:top_n]:
        try:
            trans = n.split("trans::", 1)[1]
            a, b = trans.split("->", 1)
        except Exception:
            a, b = trans, ""
        # 定位该转换在序列中的首次位置（便于人工核验）
        pos = -1
        for i in range(len(seq) - 1):
            if seq[i] == a and seq[i + 1] == b:
                pos = i
                break
        top_transitions.append({"transition": f"{a} -> {b}", "contribution": float(v), "position": pos})

    bias = float(contrib[-1]) if len(contrib) == len(feature_names) + 1 else 0.0
    result = {"top_nodes": top_nodes, "top_transitions": top_transitions, "bias": bias}

    # 附加原始日志（可选）
    if original_df is not None and key_type and key_values:
        try:
            df = original_df.copy()
            df["__time_sec"] = df["__time__"].apply(_to_epoch_seconds)
            df["action_ts"] = pd.to_datetime(df["action_time"], errors="coerce").astype("int64") / 1e9
            df = df[~df["__time_sec"].isna() & ~df["action_ts"].isna()].copy()
            df = df[df["__time_sec"] <= df["action_ts"]].copy()
            if key_type == "request_id" and "request_id" in df.columns:
                df_req = df[df["request_id"] == key_values.get("request_id")].sort_values("__time_sec")
            elif key_type == "action_instance" and {"action_time", "instance_id"}.issubset(df.columns):
                df_req = df[(df["action_time"] == key_values.get("action_time")) & (df["instance_id"] == key_values.get("instance_id"))].sort_values(
                    "__time_sec"
                )
            else:
                df_req = pd.DataFrame()

            logs = []
            if not df_req.empty:
                for _, r in df_req.iterrows():
                    logs.append(
                        {
                            "__time__": str(r.get("__time__", "")),
                            "action_time": str(r.get("action_time", "")),
                            "cluster_id": str(r.get("cluster_id", "")),
                            "content": str(r.get("content", "")).replace("\n", " "),
                        }
                    )
            result["raw_logs"] = logs

            # 为每个Top转换补充FROM/TO原始日志片段（按该请求序列中的首次位置）
            if not df_req.empty and "cluster_id" in df_req.columns:
                cluster_seq = df_req["cluster_id"].astype(str).tolist()
                for titem in result.get("top_transitions", []):
                    trans_str = titem.get("transition", "")
                    parts = trans_str.split("->")
                    if len(parts) != 2:
                        continue
                    a = parts[0].strip()
                    b = parts[1].strip()
                    found = False
                    for idx in range(len(cluster_seq) - 1):
                        if cluster_seq[idx] == a and cluster_seq[idx + 1] == b:
                            r_a = df_req.iloc[idx]
                            r_b = df_req.iloc[idx + 1]
                            titem["from_log"] = {
                                "idx": int(idx),
                                "__time__": str(r_a.get("__time__", "")),
                                "action_time": str(r_a.get("action_time", "")),
                                "cluster_id": str(r_a.get("cluster_id", "")),
                                "content": str(r_a.get("content", "")).replace("\n", " "),
                            }
                            titem["to_log"] = {
                                "idx": int(idx + 1),
                                "__time__": str(r_b.get("__time__", "")),
                                "action_time": str(r_b.get("action_time", "")),
                                "cluster_id": str(r_b.get("cluster_id", "")),
                                "content": str(r_b.get("content", "")).replace("\n", " "),
                            }
                            titem["df_position"] = int(idx)
                            found = True
                            break
                    if not found:
                        titem["from_log"] = None
                        titem["to_log"] = None

                # 为Top节点补充示例原始日志（最多3条）
                for nitem in result.get("top_nodes", []):
                    node_id = str(nitem.get("node", ""))
                    node_logs = []
                    if node_id:
                        sub = df_req[df_req["cluster_id"].astype(str) == node_id].head(3)
                        for _, r in sub.iterrows():
                            node_logs.append(
                                {
                                    "__time__": str(r.get("__time__", "")),
                                    "action_time": str(r.get("action_time", "")),
                                    "cluster_id": str(r.get("cluster_id", "")),
                                    "content": str(r.get("content", "")).replace("\n", " "),
                                }
                            )
                    nitem["examples"] = node_logs
        except Exception as e:
            result["raw_logs_error"] = str(e)

    return result


def compute_global_failure_stats(ds: pd.DataFrame) -> Dict[str, Any]:
    """
    计算全局关键转换、关键节点统计（用于模型2解释）
    返回：
      - trans_stats[(a,b)] = {total, failures, failure_rate, relative_risk}
      - node_stats[node] = {total, failures, failure_rate, relative_risk}
    """
    # 转换统计
    trans_total = Counter()
    trans_fail = Counter()
    node_total = Counter()
    node_fail = Counter()

    for _, row in ds.iterrows():
        seq = row["pattern_sequence"]
        label = int(row["label_mapped"])  # 1 failed
        # transitions
        for i in range(len(seq) - 1):
            key = (seq[i], seq[i + 1])
            trans_total[key] += 1
            if label == 1:
                trans_fail[key] += 1
        # nodes
        for n in seq:
            node_total[n] += 1
            if label == 1:
                node_fail[n] += 1

    overall_fail_rate = ds["label_mapped"].mean() if len(ds) > 0 else 0.0

    trans_stats = {}
    for k, tot in trans_total.items():
        fail = trans_fail.get(k, 0)
        if tot >= 5:  # 频次过滤
            fr = fail / tot
            rr = (fr / overall_fail_rate) if overall_fail_rate > 0 else 0.0
            trans_stats[k] = {"total": int(tot), "failures": int(fail), "failure_rate": float(fr), "relative_risk": float(rr)}

    node_stats = {}
    for n, tot in node_total.items():
        fail = node_fail.get(n, 0)
        if tot >= 10:  # 频次过滤
            fr = fail / tot
            rr = (fr / overall_fail_rate) if overall_fail_rate > 0 else 0.0
            node_stats[n] = {"total": int(tot), "failures": int(fail), "failure_rate": float(fr), "relative_risk": float(rr)}

    return {"overall_failure_rate": float(overall_fail_rate), "trans_stats": trans_stats, "node_stats": node_stats}


def analyze_failed_sequence(seq: List[str], global_stats: Dict[str, Any], top_n: int = 5) -> Dict[str, Any]:
    """
    模型2：给定失败序列，找到TopN关键转换与关键节点（基于全局统计）
    """
    trans_stats = global_stats.get("trans_stats", {})
    node_stats = global_stats.get("node_stats", {})

    # 转换打分：优先相对风险、失败率、总次数
    seq_trans = []
    for i in range(len(seq) - 1):
        key = (seq[i], seq[i + 1])
        if key in trans_stats:
            st = trans_stats[key]
            seq_trans.append(
                {
                    "transition": f"{key[0]} -> {key[1]}",
                    "failure_rate": st["failure_rate"],
                    "relative_risk": st["relative_risk"],
                    "total": st["total"],
                    "position": i,
                }
            )
    seq_trans_sorted = sorted(seq_trans, key=lambda x: (x["relative_risk"], x["failure_rate"], x["total"]), reverse=True)[:top_n]

    # 节点打分
    seq_nodes = []
    for i, n in enumerate(seq):
        if n in node_stats:
            st = node_stats[n]
            seq_nodes.append({"node": n, "failure_rate": st["failure_rate"], "relative_risk": st["relative_risk"], "total": st["total"], "position": i})
    seq_nodes_sorted = sorted(seq_nodes, key=lambda x: (x["relative_risk"], x["failure_rate"], x["total"]), reverse=True)[:top_n]

    return {"top_transitions": seq_trans_sorted, "top_nodes": seq_nodes_sorted}


def save_eval_report(output_dir: str, eval_result: Dict[str, Any]):
    os.makedirs(output_dir, exist_ok=True)
    report_path = os.path.join(output_dir, "sequence_classifier_eval.json")
    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(
            {
                "accuracy": eval_result.get("accuracy"),
                "classification_report": eval_result.get("report"),
                "confusion_matrix": eval_result.get("confusion_matrix"),
            },
            f,
            ensure_ascii=False,
            indent=2,
        )
    print(f"✓ 分类评估报告已保存: {report_path}")


# %% 简单Runner（示例用）
if __name__ == "__main__":
    clustered_dir = os.path.join(BASE_PATH, "output", "control_log_diagnose", "clustered_data")
    artifacts_dir = os.path.join(BASE_PATH, "control_log_diagnose", "artifacts")
    os.makedirs(artifacts_dir, exist_ok=True)

    print("加载并构建序列数据集...")
    ds = load_sequences_from_clustered(clustered_dir)
    print(f"总序列数: {len(ds)} | 标签分布: {ds['label_mapped'].value_counts().to_dict()}")

    print("训练分类模型(LogReg, XGB)...")
    eval_lr = train_sequence_classifier(ds)
    eval_xgb = train_xgb_classifier(ds)
    print(f"LogReg Acc: {eval_lr['accuracy']:.4f} | XGB Acc: {eval_xgb['accuracy']:.4f}")

    print("计算全局失败统计（用于解释）...")
    global_stats = compute_global_failure_stats(ds)
    print(f"统计转换数: {len(global_stats['trans_stats'])} | 节点数: {len(global_stats['node_stats'])}")

    # 保存评估报告（使用XGB结果）
    save_eval_report(artifacts_dir, eval_xgb)

    # 演示对一个失败样本进行解释（统计型 + 学习型, 并附原始日志）（与sequence_transformer.py相同）
    # Sample 10 failed samples from each file
    failed_samples = ds[ds["label_mapped"] == 1].groupby("file").head(10).reset_index(drop=True)
    if not failed_samples.empty:
        row0 = failed_samples.iloc[0]
        seq = row0["pattern_sequence"]
        explain_stat = analyze_failed_sequence(seq, global_stats, top_n=5)

        # 加载原始DF以提取原始日志
        src_file = row0.get("file")
        orig_df = None
        try:
            fpath = os.path.join(BASE_PATH, "output", "control_log_diagnose", "clustered_data", src_file)
            orig_df = pd.read_parquet(fpath, engine="pyarrow")
        except Exception:
            pass

        explain_xgb = explain_failed_with_xgb(
            seq,
            eval_xgb["model"],
            eval_xgb["vectorizer"],
            top_n=5,
            original_df=orig_df,
            key_type=row0.get("key_type"),
            key_values=row0.get("key_values"),
        )

        explain_path = os.path.join(artifacts_dir, "failed_case_explain_example.json")
        with open(explain_path, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "sample_meta": {"file": src_file, "key_type": row0.get("key_type"), "key_values": row0.get("key_values")},
                    "stat": explain_stat,
                    "xgb": explain_xgb,
                },
                f,
                ensure_ascii=False,
                indent=2,
            )
        print(f"✓ 失败样本解释已保存: {explain_path}")

# %%
