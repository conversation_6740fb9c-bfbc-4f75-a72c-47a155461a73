# Algorithm 算法项目集

## 📋 项目概览

这是一个综合性的算法项目集合，包含多个机器学习和深度学习项目的实现和实验。

## 🗂️ 项目结构

### 🎯 核心项目

#### [Failure Prediction 故障预测](./failure_prediction/)
- **位置**: `./failure_prediction/`
- **技术栈**: PyTorch, PyTorch Lightning, Scikit-learn
- **核心功能**:
  - 时序异常检测和故障预测
  - 智能序列特征提取（相邻时间去重）
  - 多模型架构支持（传统ML + 深度学习）
  - 完整的实验跟踪和性能评估

**快速开始**:
```bash
cd failure_prediction
conda activate algo
python lightning_model.py
```

**📚 文档**: [failure_prediction/docs/](./failure_prediction/docs/)

#### [Change Correlation 变更相关性分析](./change_correlation/)
- **位置**: `./change_correlation/`
- **技术栈**: Python, 统计分析, 可视化
- **核心功能**:
  - 变更事件相关性分析
  - 因果关系挖掘
  - 实验案例分析

#### [Control Log Diagnose 控制日志诊断](./control_log_diagnose/)
- **位置**: `./control_log_diagnose/`
- **技术栈**: NLP, 深度学习, 日志分析
- **核心功能**:
  - 日志异常检测
  - 序列分类和诊断
  - Transformer模型应用

### 📊 数据目录

#### [Data 数据集](./data/)
- **位置**: `./data/`
- **内容**:
  - `ecs_deploy_key_metric_clean_noise/`: ECS部署关键指标数据
  - `failure_prediction/`: 故障预测相关数据

### 🤖 模型目录

#### [AutoGluon Models](./AutogluonModels/)
- **位置**: `./AutogluonModels/`
- **内容**: AutoGluon自动机器学习模型

#### [Output 输出结果](./output/)
- **位置**: `./output/`
- **内容**:
  - `change_correlation/`: 变更相关性分析结果
  - `control_log_diagnose/`: 控制日志诊断结果

#### [SwanLog 实验日志](./swanlog/)
- **位置**: `./swanlog/`
- **内容**: SwanLab实验跟踪日志

## 🚀 环境配置

### Conda环境
项目使用统一的Conda环境管理：

```bash
# 激活环境
conda activate algo

# 或创建新环境
conda env create -f environment.yml
```

### 环境依赖
主要依赖包已在 `environment.yml` 中定义，包括：
- **深度学习**: PyTorch, PyTorch Lightning, Transformers
- **机器学习**: Scikit-learn, XGBoost, LightGBM, CatBoost
- **数据处理**: Pandas, NumPy, Polars
- **可视化**: Matplotlib, Seaborn, Plotly
- **实验跟踪**: SwanLab, MLflow

## 📖 使用指南

### 1. 故障预测项目
```bash
# 进入项目目录
cd failure_prediction

# 查看文档
cat docs/README.md

# 运行数据预处理
python preprocess.py

# 训练模型
python lightning_model.py
```

### 2. 其他项目
```bash
# 变更相关性分析
cd change_correlation
python experiments/case_analysis.py

# 控制日志诊断
cd control_log_diagnose
python src2/sequence_modeling.py
```

## 🔧 开发规范

### 代码结构
```
项目名/
├── docs/           # 📚 项目文档
├── src/            # 💻 源代码
├── models/         # 🤖 训练模型
├── logs/           # 📈 训练日志
├── data/           # 📦 数据文件
└── tests/          # 🧪 测试代码
```

### 文档规范
- 所有项目使用 `docs/` 文件夹存放文档
- 提供详细的README.md索引
- 包含使用示例和API说明

### 实验管理
- 使用SwanLab进行实验跟踪
- 记录所有重要的超参数和结果
- 保持实验可重现性

## 📈 项目特色

### 🎯 故障预测项目亮点
- **智能去重算法**: 相邻时间去重，避免信息丢失
- **多模型融合**: 结合传统ML和深度学习优势
- **完整评估体系**: 多维度性能指标分析
- **生产级代码**: 使用PyTorch Lightning框架

### 🔬 实验方法
- **系统性对比**: 不同模型和策略的全面对比
- **详细分析**: 性能分解和错误模式分析
- **可重现性**: 完整的实验配置和随机种子控制

### 📊 数据处理
- **时序特征**: 充分利用时间序列信息
- **异常检测**: 专门处理不平衡分类问题
- **特征工程**: 领域知识驱动的特征提取

## 🤝 贡献指南

### 项目开发
1. 遵循统一的代码结构
2. 添加完整的文档说明
3. 包含必要的测试代码
4. 记录实验过程和结果

### 文档维护
1. 及时更新README文件
2. 保持文档格式统一
3. 添加使用示例和最佳实践

## 📞 技术支持

各项目都有详细的文档说明：
- [故障预测文档](./failure_prediction/docs/)
- [变更相关性文档](./change_correlation/docs/)

如遇问题，请参考相应项目的文档或代码注释。

---

## 🎯 快速导航

| 项目 | 状态 | 主要技术 | 文档 |
|------|------|----------|------|
| 故障预测 | ✅ 完成 | PyTorch Lightning, 时序建模 | [📚](./failure_prediction/docs/) |
| 变更相关性 | 🔄 进行中 | 统计分析, 可视化 | [📚](./change_correlation/docs/) |
| 控制日志诊断 | 🔄 进行中 | NLP, Transformer | 暂无 |

**最后更新**: 2025年1月12日
