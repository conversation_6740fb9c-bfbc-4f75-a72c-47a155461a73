#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
import joblib
import os


class CorrelationPredictor:
    """发布-异常相关性预测器"""

    def __init__(self, model_path=None):
        self.model = None
        self.feature_names = None
        self.label_encoders = {}
        self.model_path = model_path

    def train_and_save(self, data_path):
        """训练模型并保存"""
        # 加载数据
        data = pd.read_csv(data_path)

        # 创建标签
        data["correlation_label"] = 0
        strong_positive = (data["break_reasonable_flag"] == 1) & (data["break_impact_flag"] == 1)
        data.loc[strong_positive, "correlation_label"] = 1
        strong_negative = data["is_pre_release_anomaly"] == True
        data.loc[strong_negative, "correlation_label"] = 0
        semantic_positive = (
            (data["feature_similarity_deploy_exception"] > 0.3) & (data["deploy_exception_abs_duration"] < 120) & (data["release_hit_rate"] > 0.1)
        )
        data.loc[semantic_positive, "correlation_label"] = 1
        noise_negative = (data["exception_noise_rate"] > 0.7) & (data["release_hit_rate"] < 0.01)
        data.loc[noise_negative, "correlation_label"] = 0

        # 选择特征
        self.feature_names = [
            # 时间特征
            "deploy_exception_abs_duration",  # 发布与异常的绝对时间间隔
            "is_pre_release_anomaly",  # 异常是否发生在发布之前
            "deploy_hour",  # 发布的小时
            "day_of_week_deploy",  # 发布的星期几
            "is_weekend_deploy",  # 是否在周末发布
            "exception_duration_minutes",  # 异常持续时间（分钟）
            # 统计特征
            "release_hit_rate",  # 发布命中率
            "exception_noise_rate",  # 异常噪音率
            "anomaly_density_on_family",  # 异常在服务族的密度
            "exception_severity_score",  # 异常严重程度评分
            "is_long_duration_exception",  # 是否为长时间异常
            # 语义特征
            "feature_similarity_deploy_exception",  # 发布与异常的语义相似度
            "feature_similarity_deploy_reason",  # 发布与异常原因的语义相似度
            "deploy_content_length",  # 发布内容长度
            "exception_semantics_length",  # 异常语义长度
            # 结构化特征
            "restarted_services_count",  # 重启服务数量
            "installed_rpms_count",  # 安装的RPM包数量
            "involves_nc_service",  # 是否涉及NC服务
            "involves_agent_service",  # 是否涉及Agent服务
            "involves_kvm_service",  # 是否涉及KVM服务
            "involves_network_service",  # 是否涉及网络服务
            "involves_storage_service",  # 是否涉及存储服务
            "involves_houyi_rpm",  # 是否涉及后羿RPM包
            "involves_agent-hook_rpm",  # 是否涉及Agent-hook RPM包
            "involves_kvm-config_rpm",  # 是否涉及KVM配置RPM包
            "involves_libvirt_rpm",  # 是否涉及Libvirt RPM包
            "is_performance_exception",  # 是否为性能异常
            "is_unavailable_exception",  # 是否为不可用异常
            "is_local_disk_bool",  # 是否涉及本地磁盘
            # 原始重要特征
            "duration",  # 持续时间
            "deploy_exception_duration",  # 发布异常持续时间
            "deploy_exception_nc_count",  # 发布异常影响的NC数量
            "deploy_nc_count",  # 发布涉及的NC数量
            "exception_nc_count",  # 异常影响的NC数量
            "exception_keymetric_nc_count",  # 异常关键指标影响的NC数量
        ]

        # 过滤可用特征
        available_features = [col for col in self.feature_names if col in data.columns]
        X = data[available_features].copy()

        # 处理分类特征
        categorical_features = ["time_of_day_deploy"]
        for col in categorical_features:
            if col in X.columns:
                le = LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))
                self.label_encoders[col] = le

        # 处理缺失值
        X = X.fillna(X.mean())
        y = data["correlation_label"]

        # 训练模型
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X, y)

        # 保存模型
        if self.model_path:
            joblib.dump({"model": self.model, "feature_names": available_features, "label_encoders": self.label_encoders}, self.model_path)
            print(f"模型已保存到: {self.model_path}")

        return self.model

    def load_model(self, model_path):
        """加载模型"""
        model_data = joblib.load(model_path)
        self.model = model_data["model"]
        self.feature_names = model_data["feature_names"]
        self.label_encoders = model_data["label_encoders"]
        print(f"模型已从 {model_path} 加载")

    def predict_single(self, features_dict):
        """预测单个样本"""
        if self.model is None:
            raise ValueError("模型未训练或加载")

        # 创建DataFrame
        sample = pd.DataFrame([features_dict])

        # 确保包含所有特征
        for feature in self.feature_names:
            if feature not in sample.columns:
                sample[feature] = 0  # 默认值

        # 选择特征
        X = sample[self.feature_names]

        # 处理分类特征
        for col, le in self.label_encoders.items():
            if col in X.columns:
                X[col] = le.transform(X[col].astype(str))

        # 处理缺失值
        X = X.fillna(X.mean())

        # 预测
        probability = self.model.predict_proba(X)[0]
        prediction = self.model.predict(X)[0]

        return {
            "prediction": int(prediction),
            "probability_not_correlated": probability[0],
            "probability_correlated": probability[1],
            "confidence": max(probability),
        }

    def predict_batch(self, data):
        """批量预测"""
        if self.model is None:
            raise ValueError("模型未训练或加载")

        # 选择特征
        X = data[self.feature_names]

        # 处理分类特征
        for col, le in self.label_encoders.items():
            if col in X.columns:
                X[col] = le.transform(X[col].astype(str))

        # 处理缺失值
        X = X.fillna(X.mean())

        # 预测
        predictions = self.model.predict(X)
        probabilities = self.model.predict_proba(X)

        return {"predictions": predictions, "probabilities": probabilities}

    def explain_prediction(self, features_dict):
        """解释预测结果"""
        result = self.predict_single(features_dict)

        # 获取特征重要性
        feature_importance = pd.DataFrame({"feature": self.feature_names, "importance": self.model.feature_importances_}).sort_values(
            "importance", ascending=False
        )

        print(f"预测结果: {'相关' if result['prediction'] == 1 else '不相关'}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"相关概率: {result['probability_correlated']:.3f}")
        print(f"不相关概率: {result['probability_not_correlated']:.3f}")
        print("\n关键影响因素 (Top 5):")
        for i, row in feature_importance.head(5).iterrows():
            feature_value = features_dict.get(row["feature"], "N/A")
            print(f"- {row['feature']}: {row['importance']:.3f} (值: {feature_value})")

        return result


# 使用示例
if __name__ == "__main__":
    # 数据路径
    data_path = "/Users/<USER>/GitFolders/algorithm/data/ecs_deploy_key_metric_clean_noise/features_enhanced.csv"
    model_path = "/Users/<USER>/GitFolders/algorithm/correlation_predictor.pkl"

    # 创建预测器
    predictor = CorrelationPredictor(model_path)

    # 训练并保存模型
    print("训练模型...")
    predictor.train_and_save(data_path)

    # 示例预测
    print("\n示例预测:")
    sample_features = {
        "deploy_exception_abs_duration": 30,  # 发布后30分钟出现异常
        "deploy_hour": 14,  # 下午2点发布
        "day_of_week_deploy": 1,  # 周二
        "is_weekend_deploy": False,
        "exception_duration_minutes": 60,  # 异常持续60分钟
        "release_hit_rate": 0.1,  # 发布命中率10%
        "exception_noise_rate": 0.2,  # 异常噪音率20%
        "anomaly_density_on_family": 0.05,
        "exception_severity_score": 6.0,
        "is_long_duration_exception": False,
        "feature_similarity_deploy_exception": 0.4,  # 语义相似度40%
        "feature_similarity_deploy_reason": 0.2,
        "deploy_content_length": 25,
        "exception_semantics_length": 80,
        "restarted_services_count": 0,
        "installed_rpms_count": 2,
        "involves_nc_service": False,
        "involves_agent_service": True,
        "involves_kvm_service": True,
        "involves_network_service": False,
        "involves_storage_service": False,
        "involves_houyi_rpm": True,
        "involves_agent-hook_rpm": True,
        "involves_kvm-config_rpm": True,
        "involves_libvirt_rpm": False,
        "is_performance_exception": False,
        "is_unavailable_exception": True,
        "is_local_disk_bool": False,
        "duration": 60,
        "deploy_exception_duration": 30,
        "deploy_exception_nc_count": 5,
        "deploy_nc_count": 100,
        "exception_nc_count": 20,
        "exception_keymetric_nc_count": 8,
    }

    predictor.explain_prediction(sample_features)
