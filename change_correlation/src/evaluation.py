import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, accuracy_score
from autogluon.tabular import TabularDataset, TabularPredictor
import warnings
from datetime import datetime

warnings.filterwarnings("ignore")

BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_FOLDER = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise")

current_time = datetime.now()

print(f"Current time: {current_time}")

OUTPUT_FOLDER = os.path.join(BASE_PATH, "output", "change_correlation", f"{current_time.strftime('%Y%m%d_%H%M%S')}")

if not os.path.exists(OUTPUT_FOLDER):
    os.makedirs(OUTPUT_FOLDER)
    print(f"创建输出文件夹: {OUTPUT_FOLDER}")


class CorrelationEvaluator:
    def __init__(self, data_path=None):
        """
        初始化评估器
        Args:
            data_path: 增强特征文件路径
        """
        self.data_path = data_path or os.path.join(DATA_FOLDER, "features_enhanced_tfidf.csv")
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.results = {}

    def load_data(self):
        """加载增强特征数据"""
        print("加载增强特征数据...")
        try:
            self.data = pd.read_csv(self.data_path)
            print(f"数据加载成功，形状: {self.data.shape}")
            print(f"列数: {len(self.data.columns)}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

    def create_target_labels(self):
        """
        创建目标标签 - 预测发布和异常是否相关联
        基于多个业务逻辑创建标签：
        1. break_reasonable_flag = 1 且 break_impact_flag = 1：强正样本
        2. is_pre_release_anomaly = True：强负样本（果不能先于因）
        3. 语义相似度高且时间相关性强：正样本
        4. 噪音率高且发布命中率低：负样本
        """
        print("创建目标标签...")

        # 初始化标签
        self.data["correlation_label"] = 0

        # 1. 强正样本：熔断合理且有业务影响
        strong_positive = (self.data["break_reasonable_flag"] == 1) & (self.data["break_impact_flag"] == 1)
        self.data.loc[strong_positive, "correlation_label"] = 1

        # 2. 强负样本：异常发生在发布之前
        strong_negative = self.data["is_pre_release_anomaly"] == True
        self.data.loc[strong_negative, "correlation_label"] = 0

        # 3. 基于语义相似度和时间相关性的正样本
        semantic_positive = (
            (self.data["feature_similarity_deploy_exception"] > 0.3)
            & (self.data["deploy_exception_abs_duration"] < 120)  # 2小时内
            & (self.data["release_hit_rate"] > 0.1)  # 发布命中率高
        )
        self.data.loc[semantic_positive, "correlation_label"] = 1

        # 4. 基于噪音率和发布命中率的负样本
        noise_negative = (self.data["exception_noise_rate"] > 0.7) & (self.data["release_hit_rate"] < 0.01)  # 噪音率高  # 发布命中率低
        self.data.loc[noise_negative, "correlation_label"] = 0

        # 打印标签分布
        label_counts = self.data["correlation_label"].value_counts()
        print(f"标签分布:")
        print(f"负样本 (不相关): {label_counts[0]} ({label_counts[0]/len(self.data)*100:.1f}%)")
        print(f"正样本 (相关): {label_counts[1]} ({label_counts[1]/len(self.data)*100:.1f}%)")

        return self.data["correlation_label"]

    def prepare_features(self):
        """在数据分割后准备特征数据"""
        print("准备特征数据...")

        # 选择用于训练的特征
        feature_columns = [
            # 时间特征
            "deploy_exception_abs_duration",  # 发布与异常的绝对时间间隔
            "is_pre_release_anomaly",  # 异常是否发生在发布之前
            "deploy_hour",  # 发布的小时
            "day_of_week_deploy",  # 发布的星期几
            "is_weekend_deploy",  # 是否在周末发布
            "exception_duration_minutes",  # 异常持续时间（分钟）
            # 统计特征
            "release_hit_rate",  # 发布命中率
            "exception_noise_rate",  # 异常噪音率
            "anomaly_density_on_family",  # 异常在服务族的密度
            "exception_severity_score",  # 异常严重程度评分
            "is_long_duration_exception",  # 是否为长时间异常
            # 语义特征
            "feature_similarity_deploy_exception",  # 发布与异常的语义相似度
            "feature_similarity_deploy_reason",  # 发布与异常原因的语义相似度
            "deploy_content_length",  # 发布内容长度
            "exception_semantics_length",  # 异常语义长度
            # 结构化特征
            "restarted_services_count",  # 重启服务数量
            "installed_rpms_count",  # 安装的RPM包数量
            "involves_nc_service",  # 是否涉及NC服务
            "involves_agent_service",  # 是否涉及Agent服务
            "involves_kvm_service",  # 是否涉及KVM服务
            "involves_network_service",  # 是否涉及网络服务
            "involves_storage_service",  # 是否涉及存储服务
            "involves_houyi_rpm",  # 是否涉及后羿RPM包
            "involves_agent-hook_rpm",  # 是否涉及Agent-hook RPM包
            "involves_kvm-config_rpm",  # 是否涉及KVM配置RPM包
            "involves_libvirt_rpm",  # 是否涉及Libvirt RPM包
            "is_performance_exception",  # 是否为性能异常
            "is_unavailable_exception",  # 是否为不可用异常
            "is_local_disk_bool",  # 是否涉及本地磁盘
            # 原始重要特征
            "duration",  # 持续时间
            "deploy_exception_duration",  # 发布异常持续时间
            "deploy_exception_nc_count",  # 发布异常影响的NC数量
            "deploy_nc_count",  # 发布涉及的NC数量
            "exception_nc_count",  # 异常影响的NC数量
            "exception_keymetric_nc_count",  # 异常关键指标影响的NC数量
        ]

        # 过滤存在的特征
        available_features = [col for col in feature_columns if col in self.data.columns]
        print(f"可用特征数: {len(available_features)}")

        # 基于已分割的索引准备训练集和测试集
        if hasattr(self, "train_indices") and hasattr(self, "test_indices"):
            # 准备特征矩阵
            X_all = self.data[available_features].copy()

            # 处理分类特征
            categorical_features = ["time_of_day_deploy"]
            for col in categorical_features:
                if col in X_all.columns:
                    le = LabelEncoder()
                    X_all[col] = le.fit_transform(X_all[col].astype(str))

            # 处理缺失值
            X_all = X_all.fillna(X_all.mean())

            # 根据预先分割的索引分离训练集和测试集
            self.X_train = X_all.loc[self.train_indices].copy()
            self.X_test = X_all.loc[self.test_indices].copy()

            # 准备目标变量
            y_all = self.data["correlation_label"]
            self.y_train = y_all.loc[self.train_indices].copy()
            self.y_test = y_all.loc[self.test_indices].copy()

            print(f"训练集大小: {self.X_train.shape}")
            print(f"测试集大小: {self.X_test.shape}")
            print(f"训练集正样本比例: {self.y_train.mean():.3f}")
            print(f"测试集正样本比例: {self.y_test.mean():.3f}")

        else:
            raise ValueError("必须先调用 split_data() 方法进行数据分割")

        return available_features

    def split_data(self, test_size=0.2, random_state=42):
        """
        先基于原始语义字段划分训练集和测试集，避免信息泄漏
        确保具有相同语义特征的样本被分配到同一个数据集中
        """
        print("基于原始语义字段划分训练集和测试集...")

        # 创建目标标签
        y = self.create_target_labels()

        # 识别语义相关的原始字段
        semantic_columns = []

        # 检查原始数据中可能存在的语义相关字段
        potential_semantic_cols = ["deploy_task"]

        for col in potential_semantic_cols:
            if col in self.data.columns:
                semantic_columns.append(col)

        if semantic_columns:
            print(f"发现语义字段: {semantic_columns}")

            # 基于原始语义字段创建分组标识
            semantic_keys = []
            for _, row in self.data.iterrows():
                key_parts = []
                for col in semantic_columns:
                    # 处理空值和标准化文本
                    value = str(row[col]) if pd.notna(row[col]) else "NONE"
                    # 简单的文本标准化（去除空格、转小写）
                    value = value.strip().lower()
                    key_parts.append(value)
                semantic_keys.append("|".join(key_parts))

            # 将语义键添加到数据中
            self.data["semantic_group"] = semantic_keys

            # 按语义组进行分组
            unique_groups = np.array(list(set(semantic_keys)))
            print(f"发现 {len(unique_groups)} 个唯一的语义组")

            # 计算每个组的样本数和标签分布
            group_stats = []
            for group in unique_groups:
                group_mask = self.data["semantic_group"] == group
                group_size = group_mask.sum()
                group_labels = y[group_mask]
                pos_ratio = group_labels.mean() if len(group_labels) > 0 else 0
                group_stats.append({"group": group, "size": group_size, "pos_ratio": pos_ratio})

            group_stats_df = pd.DataFrame(group_stats)
            print(f"语义组统计: 平均大小={group_stats_df['size'].mean():.1f}, " f"正样本比例={group_stats_df['pos_ratio'].mean():.3f}")

            # 分层分割语义组以保持标签分布
            # 根据正样本比例对组进行分层
            high_pos_groups = group_stats_df[group_stats_df["pos_ratio"] > 0.5]["group"].values
            low_pos_groups = group_stats_df[group_stats_df["pos_ratio"] <= 0.5]["group"].values

            np.random.seed(random_state)

            # 分别对高正样本比例组和低正样本比例组进行分割
            n_test_high = int(len(high_pos_groups) * test_size)
            n_test_low = int(len(low_pos_groups) * test_size)

            test_groups = set()
            if len(high_pos_groups) > 0:
                shuffled_high = np.random.permutation(high_pos_groups)
                test_groups.update(shuffled_high[:n_test_high])

            if len(low_pos_groups) > 0:
                shuffled_low = np.random.permutation(low_pos_groups)
                test_groups.update(shuffled_low[:n_test_low])

            train_groups = set(unique_groups) - test_groups

            # 根据组分配样本索引
            train_indices = self.data[self.data["semantic_group"].isin(train_groups)].index
            test_indices = self.data[self.data["semantic_group"].isin(test_groups)].index

            # 保存索引，特征准备时会用到
            self.train_indices = train_indices
            self.test_indices = test_indices
            self.semantic_split_used = True

            print(f"训练组数: {len(train_groups)}, 测试组数: {len(test_groups)}")
            print(f"训练样本数: {len(train_indices)}, 测试样本数: {len(test_indices)}")

            # 验证分割质量
            train_pos_ratio = y[train_indices].mean()
            test_pos_ratio = y[test_indices].mean()
            print(f"训练集正样本比例: {train_pos_ratio:.3f}")
            print(f"测试集正样本比例: {test_pos_ratio:.3f}")

            # 验证语义分割质量
            self._validate_semantic_split(semantic_columns, train_indices, test_indices)

        else:
            print("未发现语义字段，使用传统分割方法...")
            # 如果没有语义字段，回退到传统分割方法
            train_indices, test_indices = train_test_split(self.data.index, test_size=test_size, random_state=random_state, stratify=y)
            self.train_indices = train_indices
            self.test_indices = test_indices
            self.semantic_split_used = False

        return len(train_indices), len(test_indices)

    def train_traditional_models(self):
        """训练传统机器学习模型"""
        print("训练传统机器学习模型...")

        # 定义模型
        models = {
            "Random Forest": RandomForestClassifier(n_estimators=100, random_state=42),
            "Gradient Boosting": GradientBoostingClassifier(n_estimators=100, random_state=42),
            "Logistic Regression": LogisticRegression(random_state=42, max_iter=1000),
            "SVM": SVC(kernel="rbf", probability=True, random_state=42),
        }

        # 训练和评估每个模型
        for name, model in models.items():
            print(f"\n训练 {name}...")

            # 训练模型
            model.fit(self.X_train, self.y_train)

            # 预测
            y_pred = model.predict(self.X_test)
            y_pred_proba = model.predict_proba(self.X_test)[:, 1]

            # 评估
            accuracy = accuracy_score(self.y_test, y_pred)
            auc = roc_auc_score(self.y_test, y_pred_proba)

            # 交叉验证
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5, scoring="roc_auc")

            # 保存结果
            self.models[name] = model
            self.results[name] = {
                "accuracy": accuracy,
                "auc": auc,
                "cv_auc_mean": cv_scores.mean(),
                "cv_auc_std": cv_scores.std(),
                "y_pred": y_pred,
                "y_pred_proba": y_pred_proba,
            }

            print(f"准确率: {accuracy:.4f}")
            print(f"AUC: {auc:.4f}")
            print(f"交叉验证 AUC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

    def train_autogluon_model(self, time_limit=300):
        """使用AutoGluon训练模型"""
        print("使用AutoGluon训练模型...")

        try:
            # 准备数据
            train_data = self.X_train.copy()
            train_data["correlation_label"] = self.y_train

            test_data = self.X_test.copy()
            test_data["correlation_label"] = self.y_test

            # 转换为TabularDataset
            train_data = TabularDataset(train_data)
            test_data = TabularDataset(test_data)

            # 创建预测器
            predictor = TabularPredictor(
                label="correlation_label",
                problem_type="binary",
                eval_metric="roc_auc",
                path=os.path.join(OUTPUT_FOLDER, "autogluon_models"),
            )

            # 训练模型 - 启用GPU加速 with reduced resource usage
            import torch
            torch.cuda.empty_cache()  # Clear GPU cache
            
            predictor.fit(
                train_data,
                time_limit=time_limit,
                presets="best_quality",
                holdout_frac=0.2,
                ag_args_fit={
                    "num_gpus": 1, 
                    "max_memory_usage_ratio": 0.6,  # Reduced memory usage
                    "num_cpus": 4  # Limit CPU usage to reduce Ray worker load
                },
            )

            # 预测
            y_pred_proba = predictor.predict_proba(test_data)
            if hasattr(y_pred_proba, "values"):
                y_pred_proba = y_pred_proba.values
            y_pred = (y_pred_proba > 0.5).astype(int)

            # 评估
            accuracy = accuracy_score(self.y_test, y_pred)
            auc = roc_auc_score(self.y_test, y_pred_proba)

            # 保存结果
            self.models["AutoGluon"] = predictor
            self.results["AutoGluon"] = {"accuracy": accuracy, "auc": auc, "y_pred": y_pred, "y_pred_proba": y_pred_proba}

            print(f"AutoGluon 准确率: {accuracy:.4f}")
            print(f"AutoGluon AUC: {auc:.4f}")

            # 显示排行榜
            leaderboard = predictor.leaderboard(test_data, silent=True)
            print("\nAutoGluon 模型排行榜:")
            print(leaderboard.head())

        except Exception as e:
            print(f"AutoGluon 训练失败: {e}")

    def evaluate_models(self):
        """评估所有模型"""
        print("\n=== 模型评估结果 ===")

        # 创建结果汇总
        summary = []
        for name, result in self.results.items():
            summary.append(
                {
                    "Model": name,
                    "Accuracy": result["accuracy"],
                    "AUC": result["auc"],
                    "CV AUC Mean": result.get("cv_auc_mean", "N/A"),
                    "CV AUC Std": result.get("cv_auc_std", "N/A"),
                }
            )

        summary_df = pd.DataFrame(summary)
        print(summary_df.to_string(index=False))

        # 找到最佳模型
        best_model_name = max(self.results.keys(), key=lambda x: self.results[x]["auc"])
        print(f"\n最佳模型: {best_model_name} (AUC: {self.results[best_model_name]['auc']:.4f})")

        # 详细评估最佳模型
        best_result = self.results[best_model_name]
        print(f"\n{best_model_name} 详细评估:")
        print("分类报告:")
        print(classification_report(self.y_test, best_result["y_pred"]))

        print("混淆矩阵:")
        cm = confusion_matrix(self.y_test, best_result["y_pred"])
        print(cm)

        return best_model_name, summary_df

    def sample_confusion_matrix_cases(self, model_name=None, sample_ratio=0.1, min_samples_per_category=5):
        """
        从混淆矩阵的四类案例 (TP, FP, TN, FN) 中各抽取一定比例的样本用于人工验证

        Args:
            model_name: 要分析的模型名称，如果为None则使用最佳模型
            sample_ratio: 每类案例的抽样比例
            min_samples_per_category: 每类最少抽样数量
        """
        print(f"\n=== 混淆矩阵案例抽样 ===")

        # 确定要使用的模型
        if model_name is None:
            model_name = max(self.results.keys(), key=lambda x: self.results[x]["auc"])

        if model_name not in self.results:
            print(f"错误: 模型 '{model_name}' 未找到")
            return None

        result = self.results[model_name]
        y_true = self.y_test
        y_pred = result["y_pred"]

        # 计算混淆矩阵的四个类别索引
        tp_indices = np.where((y_true == 1) & (y_pred == 1))[0]  # True Positive
        fp_indices = np.where((y_true == 0) & (y_pred == 1))[0]  # False Positive
        tn_indices = np.where((y_true == 0) & (y_pred == 0))[0]  # True Negative
        fn_indices = np.where((y_true == 1) & (y_pred == 0))[0]  # False Negative

        print(f"混淆矩阵分布:")
        print(f"TP (真正例): {len(tp_indices)} 个样本")
        print(f"FP (假正例): {len(fp_indices)} 个样本")
        print(f"TN (真负例): {len(tn_indices)} 个样本")
        print(f"FN (假负例): {len(fn_indices)} 个样本")

        # 计算每类需要抽样的数量
        tp_sample_size = max(min_samples_per_category, int(len(tp_indices) * sample_ratio))
        fp_sample_size = max(min_samples_per_category, int(len(fp_indices) * sample_ratio))
        tn_sample_size = max(min_samples_per_category, int(len(tn_indices) * sample_ratio))
        fn_sample_size = max(min_samples_per_category, int(len(fn_indices) * sample_ratio))

        # 确保不超过实际样本数
        tp_sample_size = min(tp_sample_size, len(tp_indices))
        fp_sample_size = min(fp_sample_size, len(fp_indices))
        tn_sample_size = min(tn_sample_size, len(tn_indices))
        fn_sample_size = min(fn_sample_size, len(fn_indices))

        print(f"\n计划抽样数量:")
        print(f"TP: {tp_sample_size} 个样本 (比例: {sample_ratio:.2%})")
        print(f"FP: {fp_sample_size} 个样本 (比例: {sample_ratio:.2%})")
        print(f"TN: {tn_sample_size} 个样本 (比例: {sample_ratio:.2%})")
        print(f"FN: {fn_sample_size} 个样本 (比例: {sample_ratio:.2%})")

        # 设置随机种子以确保可重现性
        np.random.seed(42)

        # 随机抽样各类别样本
        tp_sampled = np.random.choice(tp_indices, tp_sample_size, replace=False) if len(tp_indices) > 0 else []
        fp_sampled = np.random.choice(fp_indices, fp_sample_size, replace=False) if len(fp_indices) > 0 else []
        tn_sampled = np.random.choice(tn_indices, tn_sample_size, replace=False) if len(tn_indices) > 0 else []
        fn_sampled = np.random.choice(fn_indices, fn_sample_size, replace=False) if len(fn_indices) > 0 else []

        # 创建包含原始数据和预测结果的数据框
        test_data_with_predictions = self.data.loc[self.test_indices].copy()
        test_data_with_predictions["y_true"] = y_true
        test_data_with_predictions["y_pred"] = y_pred
        test_data_with_predictions["case_type"] = "Unknown"

        # 标记各类别案例
        if len(tp_sampled) > 0:
            test_data_with_predictions.loc[test_data_with_predictions.index[tp_sampled], "case_type"] = "TP"
        if len(fp_sampled) > 0:
            test_data_with_predictions.loc[test_data_with_predictions.index[fp_sampled], "case_type"] = "FP"
        if len(tn_sampled) > 0:
            test_data_with_predictions.loc[test_data_with_predictions.index[tn_sampled], "case_type"] = "TN"
        if len(fn_sampled) > 0:
            test_data_with_predictions.loc[test_data_with_predictions.index[fn_sampled], "case_type"] = "FN"

        # 只保留抽样的案例
        sampled_cases = test_data_with_predictions[test_data_with_predictions["case_type"].isin(["TP", "FP", "TN", "FN"])].copy()

        # 添加预测概率（如果有）
        if "y_pred_proba" in result:
            # 只添加抽样案例对应的预测概率
            proba_values = []
            for idx in sampled_cases.index:
                # 找到该样本在测试集中的原始索引位置
                original_idx = list(test_data_with_predictions.index).index(idx)
                proba_values.append(result["y_pred_proba"][original_idx])
            sampled_cases["y_pred_proba"] = proba_values

        print(f"\n实际抽样完成:")
        print(f"TP 抽样: {len(sampled_cases[sampled_cases['case_type'] == 'TP'])} 个样本")
        print(f"FP 抽样: {len(sampled_cases[sampled_cases['case_type'] == 'FP'])} 个样本")
        print(f"TN 抽样: {len(sampled_cases[sampled_cases['case_type'] == 'TN'])} 个样本")
        print(f"FN 抽样: {len(sampled_cases[sampled_cases['case_type'] == 'FN'])} 个样本")

        # 保存到CSV文件供人工验证
        output_file = os.path.join(OUTPUT_FOLDER, f"confusion_matrix_cases_{model_name.replace(' ', '_')}.csv")
        sampled_cases.to_csv(output_file, index=False, encoding="utf-8")
        print(f"\n抽样案例已保存到: {output_file}")

        # 显示部分抽样案例的关键信息
        print(f"\n抽样案例预览:")
        display_columns = [
            "case_type",
            "y_true",
            "y_pred",
            "deploy_exception_abs_duration",
            "feature_similarity_deploy_exception",
            "release_hit_rate",
            "exception_noise_rate",
        ]

        # 确保这些列存在于数据中
        available_display_columns = [col for col in display_columns if col in sampled_cases.columns]
        preview_data = sampled_cases[available_display_columns].head(10)
        print(preview_data.to_string(index=False))

        return sampled_cases

    def feature_importance_analysis(self):
        """特征重要性分析"""
        print("\n=== 特征重要性分析 ===")

        # 使用随机森林进行特征重要性分析
        rf_model = self.models.get("Random Forest")
        if rf_model:
            feature_names = self.X_train.columns
            importances = rf_model.feature_importances_

            # 创建特征重要性DataFrame
            feature_importance_df = pd.DataFrame({"feature": feature_names, "importance": importances}).sort_values("importance", ascending=False)

            print("Top 15 重要特征:")
            print(feature_importance_df.head(15).to_string(index=False))

            return feature_importance_df

        return None

    def _validate_semantic_split(self, semantic_columns, train_indices, test_indices):
        """验证语义分割的质量，确保没有数据泄漏"""
        print("\n验证语义分割质量...")

        # 获取训练集和测试集的语义键
        train_semantic_keys = set()
        test_semantic_keys = set()

        for idx in train_indices:
            key_parts = []
            for col in semantic_columns:
                value = str(self.data.loc[idx, col]) if pd.notna(self.data.loc[idx, col]) else "NONE"
                value = value.strip().lower()
                key_parts.append(value)
            train_semantic_keys.add("|".join(key_parts))

        for idx in test_indices:
            key_parts = []
            for col in semantic_columns:
                value = str(self.data.loc[idx, col]) if pd.notna(self.data.loc[idx, col]) else "NONE"
                value = value.strip().lower()
                key_parts.append(value)
            test_semantic_keys.add("|".join(key_parts))

        # 检查重叠
        overlap = train_semantic_keys.intersection(test_semantic_keys)

        if overlap:
            print(f"⚠️  警告: 发现 {len(overlap)} 个重叠的语义组!")
            print("前5个重叠的语义组:")
            for i, key in enumerate(list(overlap)[:5]):
                print(f"  {i+1}. {key[:100]}...")
        else:
            print("✅ 语义分割验证通过：训练集和测试集无重叠语义组")

        print(f"训练集唯一语义组数: {len(train_semantic_keys)}")
        print(f"测试集唯一语义组数: {len(test_semantic_keys)}")
        print(f"总唯一语义组数: {len(train_semantic_keys | test_semantic_keys)}")

        return len(overlap) == 0

    def run_full_evaluation(self):
        """运行完整的评估流程"""
        print("开始完整的模型评估流程...")

        # 1. 加载数据
        if not self.load_data():
            return None

        # 2. 先划分数据（基于原始语义字段）
        train_size, test_size = self.split_data()
        print(f"数据分割完成：训练样本 {train_size}, 测试样本 {test_size}")

        # 3. 再准备特征（在分割后）
        feature_names = self.prepare_features()
        print(f"特征准备完成：{len(feature_names)} 个特征")

        # 4. 训练传统模型
        self.train_traditional_models()

        # 5. 训练AutoGluon模型
        self.train_autogluon_model()

        # 6. 评估模型
        best_model, summary = self.evaluate_models()

        # 7. 特征重要性分析
        feature_importance = self.feature_importance_analysis()

        # 8. 混淆矩阵案例抽样
        print(f"\n开始混淆矩阵案例抽样...")
        sampled_cases = self.sample_confusion_matrix_cases(model_name=best_model, sample_ratio=0.1, min_samples_per_category=5)

        return {"best_model": best_model, "summary": summary, "feature_importance": feature_importance, "sampled_cases": sampled_cases, "results": self.results}


if __name__ == "__main__":
    # 运行评估
    evaluator = CorrelationEvaluator()
    results = evaluator.run_full_evaluation()

    if results:
        print("\n=== 评估完成 ===")
        print(f"最佳模型: {results['best_model']}")
        print("详细结果已保存到 results 中")

        # 显示抽样案例信息
        if results.get("sampled_cases") is not None:
            print(f"混淆矩阵抽样案例已保存到文件中，可用于人工验证")

        import pickle as pkl

        # Save result to CSV
        output_file = os.path.join(OUTPUT_FOLDER, "evaluation_results.pkl")
        with open(output_file, "wb") as f:
            pkl.dump(results, f)

        # 也可以单独对特定模型进行案例抽样
        # sampled_cases = evaluator.sample_confusion_matrix_cases(
        #     model_name="Random Forest",
        #     sample_ratio=0.15,  # 15%的样本
        #     min_samples_per_category=10  # 每类最少10个样本
        # )
