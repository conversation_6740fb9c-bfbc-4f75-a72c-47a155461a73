import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import json
from typing import Dict, List, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
import dashscope as ds
import time
import asyncio
import aiohttp
from functools import lru_cache
import hashlib
import pyarrow as pa
import pyarrow.parquet as pq

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


BASE_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class FeatureEngineer:
    def __init__(self):        
        """初始化特征工程类，加载配置和下载器"""
        from connector.download import config, ODPSDownloader

        self.config = config
        self.downloader = ODPSDownloader(config)

    def download(self, download_config):
        """下载数据，添加缓存检查和增量更新机制"""
        if download_config.get("output_dir") is None:
            download_config["output_dir"] = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise")
        if download_config.get("output_filename") is None:
            download_config["output_filename"] = "feature.csv"
        if download_config.get("chunk_size") is None:
            download_config["chunk_size"] = 10000

        output_path = os.path.join(download_config["output_dir"], download_config["output_filename"])

        # 检查缓存文件是否存在且是最新的
        if self._is_cache_valid(output_path, download_config.get("cache_ttl_hours", 24)):
            print(f"缓存文件 {download_config['output_filename']} 有效，跳过下载。")
            return

        # 创建输出目录
        os.makedirs(download_config["output_dir"], exist_ok=True)

        # 如果有旧文件，先备份
        if os.path.exists(output_path):
            backup_path = f"{output_path}.backup.{int(time.time())}"
            os.rename(output_path, backup_path)
            print(f"已备份旧文件到: {backup_path}")

        try:
            self.downloader.download_with_custom_params(
                sql_query=download_config["sql_query"],
                output_dir=download_config["output_dir"],
                output_filename=download_config["output_filename"],
                chunk_size=download_config["chunk_size"],
            )
            print(f"数据下载完成: {output_path}")
        except Exception as e:
            print(f"数据下载失败: {e}")
            # 如果下载失败但有备份文件，恢复备份
            if os.path.exists(f"{output_path}.backup"):
                os.rename(f"{output_path}.backup", output_path)
                print(f"已恢复备份文件")
            raise

    def load_feature(self, enable_cache=True):
        """加载特征数据，支持缓存机制"""
        feature_path = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise", "feature.csv")
        cache_path = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise", "feature.parquet")

        # 检查Parquet缓存文件
        if enable_cache and os.path.exists(cache_path) and self._is_cache_valid(cache_path, 24):
            try:
                print("从Parquet缓存加载数据...")
                df = pd.read_parquet(cache_path)
                print(f"缓存加载完成，数据形状: {df.shape}")
                return df
            except Exception as e:
                print(f"缓存加载失败: {e}, 将从CSV加载数据")

        # 检查CSV原始文件
        if not os.path.exists(feature_path):
            print(f"Feature file {feature_path} does not exist.")
            return None

        print("从CSV文件加载数据...")
        df = pd.read_csv(feature_path)

        # 保存Parquet缓存以加速下次加载
        if enable_cache:
            try:
                df.to_parquet(cache_path, index=False)
                print(f"已创建Parquet缓存: {cache_path}")
            except Exception as e:
                print(f"创建缓存失败: {e}")

        print(f"CSV加载完成，数据形状: {df.shape}")
        return df

    def generate_feature(self):
        """
        生成高级特征工程特征，包括：
        1. 时间序列特征
        2. 统计与比例特征
        3. 文本语义特征
        """
        df = self.load_feature()
        if df is None:
            return None

        print("开始生成特征...")

        # 1. 时间序列特征
        df = self._generate_temporal_features(df)

        # 2. 统计与比例特征
        df = self._generate_statistical_features(df)

        # 4. 结构化特征提取
        df = self._generate_structured_features(df)

        # 3. 文本语义特征
        df = self._generate_semantic_features(df)

        print("特征生成完成!")
        return df

    def _generate_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成时间序列特征"""
        print("生成时间序列特征...")

        # 转换时间字段
        df["deploy_time"] = pd.to_datetime(df["deploy_time"])
        df["exception_start_time"] = pd.to_datetime(df["exception_start_time"])
        df["exception_end_time"] = pd.to_datetime(df["exception_end_time"])

        # 1. is_pre_release_anomaly: 异常是否发生在发布之前
        df["is_pre_release_anomaly"] = df["exception_start_time"] < df["deploy_time"]

        # 2. time_of_day_deploy: 发布时间段
        df["deploy_hour"] = df["deploy_time"].dt.hour
        df["time_of_day_deploy"] = pd.cut(df["deploy_hour"], bins=[0, 6, 12, 18, 24], labels=["00-06", "06-12", "12-18", "18-24"], include_lowest=True)

        # 3. day_of_week_deploy: 发布星期
        df["day_of_week_deploy"] = df["deploy_time"].dt.dayofweek  # 0=Monday, 6=Sunday
        df["is_weekend_deploy"] = df["day_of_week_deploy"].isin([5, 6])  # Saturday, Sunday

        # 4. 异常持续时长相关特征
        df["exception_duration_minutes"] = (df["exception_end_time"] - df["exception_start_time"]).dt.total_seconds() / 60

        # 5. 发布到异常的绝对时间差
        df["deploy_exception_abs_duration"] = abs(df["deploy_exception_duration"])

        return df

    def _generate_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成统计与比例特征"""
        print("生成统计与比例特征...")

        # 1. release_hit_rate: 发布任务中异常的命中率
        df["release_hit_rate"] = np.where(df["deploy_nc_count"] > 0, df["deploy_exception_nc_count"] / df["deploy_nc_count"], 0)

        # 2. exception_noise_rate: 异常的背景噪音率
        # 计算每天总的NC数量（需要根据实际情况调整）
        daily_stats = (
            df.groupby("ds")
            .agg(
                {
                    "nc_id": "nunique",  # 每天唯一NC数量
                    "exception_nc_count": "first",  # 异常出现的NC数量
                    "exception_keymetric_nc_count": "first",  # 关联发布的异常NC数量
                }
            )
            .reset_index()
        )

        # 计算噪音率
        df["exception_noise_count"] = df["exception_nc_count"] - df["exception_keymetric_nc_count"]
        df["exception_noise_rate"] = np.where(df["exception_nc_count"] > 0, df["exception_noise_count"] / df["exception_nc_count"], 0)

        # 3. anomaly_density_on_family: 机型族异常密度
        family_stats = (
            df.groupby(["ds", "instance_type_family", "exception_name"])
            .agg({"instance_id": "nunique", "nc_id": "nunique"})  # 该机型族当天该异常的实例数  # 该机型族当天的总NC数
            .reset_index()
        )

        family_stats["anomaly_density_on_family"] = family_stats["instance_id"] / family_stats["nc_id"]

        # 合并回原DataFrame
        df = df.merge(
            family_stats[["ds", "instance_type_family", "exception_name", "anomaly_density_on_family"]],
            on=["ds", "instance_type_family", "exception_name"],
            how="left",
        )

        # 4. 异常严重程度相关特征
        df["is_long_duration_exception"] = df["exception_duration_minutes"] > 60  # 超过1小时的异常
        df["exception_severity_score"] = df["duration"] * df["release_hit_rate"]  # 异常严重程度得分

        return df

    def _generate_semantic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成文本语义特征，使用大模型进行语义扩展和text-embedding"""
        print("生成文本语义特征...")

        # 填充空值
        df["deploy_content"] = df["deploy_content"].fillna("")
        df["exception_name"] = df["exception_name"].fillna("")
        df["xdc_component"] = df["xdc_component"].fillna("")
        df["xdc_subcomponent"] = df["xdc_subcomponent"].fillna("")
        df["tag_name"] = df["tag_name"].fillna("")
        df["break_reason"] = df["break_reason"].fillna("")

        # 创建异常语义描述
        df["exception_semantics"] = "异常名称: " + df["exception_name"] + " 部件类别: " + df["xdc_component"] + " 子部件类别: " + df["xdc_subcomponent"]

        # 使用大模型进行语义扩展
        print("使用大模型进行语义扩展...")
        df = self._enhance_with_llm_semantics(df)

        # 使用text-embedding获取高质量语义向量
        print("使用text-embedding计算语义相似度...")
        df = self._compute_embedding_similarity(
            df
        )  # embedding_similarity_deploy_exception, embedding_similarity_deploy_reason, embedding_similarity_exception_reason

        # 传统TF-IDF作为备选特征
        df = self._compute_tfidf_features(df)

        # 文本长度特征
        df["deploy_content_length"] = df["deploy_content"].str.len()
        df["exception_semantics_length"] = df["exception_semantics"].str.len()

        return df

    def _generate_structured_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成结构化特征"""
        print("生成结构化特征...")

        # 从deploy_content中提取结构化信息
        df["parsed_deploy_components"] = df["deploy_content"].apply(self._parse_deploy_content)

        # 展开结构化信息
        df["restarted_services_count"] = df["parsed_deploy_components"].apply(lambda x: len(x.get("restarted", [])))
        df["installed_rpms_count"] = df["parsed_deploy_components"].apply(lambda x: len(x.get("installed", [])))

        # 常见服务和RPM的标识
        common_services = ["nc", "agent", "kvm", "network", "storage"]
        common_rpms = ["houyi", "agent-hook", "kvm-config", "libvirt"]

        for service in common_services:
            df[f"involves_{service}_service"] = df["deploy_content"].str.contains(service, case=False, na=False)

        for rpm in common_rpms:
            df[f"involves_{rpm}_rpm"] = df["deploy_content"].str.contains(rpm, case=False, na=False)

        # 异常类型分类特征
        df["is_performance_exception"] = df["monitor_type"] == "performance"
        df["is_unavailable_exception"] = df["monitor_type"] == "unavailable"

        # 存储类型特征
        df["is_local_disk_bool"] = df["is_local_disk"] == "True"

        return df

    @lru_cache(maxsize=10000)
    def _parse_deploy_content(self, content: str) -> Dict[str, List[str]]:
        """解析deploy_content中的结构化信息，优化正则表达式性能"""
        if pd.isna(content) or not content.strip():
            return {"restarted": [], "installed": []}

        result = {"restarted": [], "installed": []}

        # 预编译正则表达式
        if not hasattr(self, "_compiled_restart_patterns"):
            self._compiled_restart_patterns = [
                re.compile(r"restart\s+([a-zA-Z0-9_-]+)", re.IGNORECASE),
                re.compile(r"service\s+([a-zA-Z0-9_-]+)\s+restart", re.IGNORECASE),
                re.compile(r"systemctl\s+restart\s+([a-zA-Z0-9_-]+)", re.IGNORECASE),
            ]
            self._compiled_rpm_patterns = [
                re.compile(r"rpm:([a-zA-Z0-9_-]+)", re.IGNORECASE),
                re.compile(r"install\s+([a-zA-Z0-9_-]+\.rpm)", re.IGNORECASE),
                re.compile(r"yum\s+install\s+([a-zA-Z0-9_-]+)", re.IGNORECASE),
            ]

        # 以分号分割内容
        content_parts = content.split(";")

        # 查找重启的服务
        for part in content_parts:
            part = part.strip()
            for pattern in self._compiled_restart_patterns:
                matches = pattern.findall(part)
                result["restarted"].extend(matches)

        # 查找安装的RPM
        for part in content_parts:
            part = part.strip()
            for pattern in self._compiled_rpm_patterns:
                matches = pattern.findall(part)
                result["installed"].extend(matches)

        # 去重
        result["restarted"] = list(set(result["restarted"]))
        result["installed"] = list(set(result["installed"]))

        return result

    def save_features(self, df: pd.DataFrame, output_path: str = None, create_backup=True):
        """保存生成的特征到文件，支持Parquet格式以提高加载速度"""
        if output_path is None:
            output_path = os.path.join(BASE_PATH, "data", "ecs_deploy_key_metric_clean_noise", "features_enhanced.csv")

        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        os.makedirs(output_dir, exist_ok=True)

        # 创建备份
        if create_backup and os.path.exists(output_path):
            backup_path = f"{output_path}.backup.{int(time.time())}"
            os.rename(output_path, backup_path)
            print(f"已备份旧文件到: {backup_path}")

        try:
            # 同时保存为CSV和Parquet格式
            csv_path = output_path
            parquet_path = output_path.replace(".csv", ".parquet")

            # 保存CSV格式（兼容性）
            df.to_csv(csv_path, index=False)
            print(f"特征已保存到CSV: {csv_path}")

            # 保存Parquet格式（高效加载）
            df.to_parquet(parquet_path, index=False)
            print(f"特征已保存到Parquet: {parquet_path}")

        except Exception as e:
            print(f"保存特征失败: {e}")
            # 如果保存失败且有备份，恢复备份
            if create_backup and os.path.exists(backup_path):
                os.rename(backup_path, output_path)
                print(f"已恢复备份文件")
            raise

        # 打印特征统计信息
        print(f"总样本数: {len(df)}")
        print(f"总特征数: {df.shape[1]}")
        print(f"发布前异常比例: {df['is_pre_release_anomaly'].mean():.4f}")

        # 传统语义相似度统计
        if "feature_similarity_deploy_exception" in df.columns:
            print(f"平均传统语义相似度: {df['feature_similarity_deploy_exception'].mean():.4f}")

        # 大模型增强的语义相似度统计
        if "embedding_similarity_deploy_exception" in df.columns:
            print(f"平均embedding语义相似度: {df['embedding_similarity_deploy_exception'].mean():.4f}")
        if "embedding_similarity_deploy_reason" in df.columns:
            print(f"平均部署-故障原因相似度: {df['embedding_similarity_deploy_reason'].mean():.4f}")
        if "embedding_similarity_exception_reason" in df.columns:
            print(f"平均异常-故障原因相似度: {df['embedding_similarity_exception_reason'].mean():.4f}")

        # TF-IDF相似度统计
        if "tfidf_similarity_deploy_exception" in df.columns:
            print(f"平均TF-IDF相似度: {df['tfidf_similarity_deploy_exception'].mean():.4f}")

        # 大模型语义扩展统计
        if "llm_correlation_reasoning" in df.columns:
            non_empty_reasoning = df[df["llm_correlation_reasoning"] != ""]
            print(f"LLM语义扩展样本数: {len(non_empty_reasoning)}")
            if len(non_empty_reasoning) > 0:
                print(f"平均扩展文本长度: {non_empty_reasoning['llm_correlation_reasoning'].str.len().mean():.1f}")

        # 文本长度统计
        if "enhanced_deploy_content" in df.columns:
            print(f"增强后部署内容平均长度: {df['enhanced_deploy_content'].str.len().mean():.1f}")
        if "enhanced_exception_semantics" in df.columns:
            print(f"增强后异常语义平均长度: {df['enhanced_exception_semantics'].str.len().mean():.1f}")

        print(f"语义特征增强完成！新增特征包括:")
        print(f"- embedding_similarity_deploy_exception: 基于text-embedding的部署-异常相似度")
        print(f"- embedding_similarity_deploy_reason: 基于text-embedding的部署-故障原因相似度")
        print(f"- embedding_similarity_exception_reason: 基于text-embedding的异常-故障原因相似度")
        print(f"- tfidf_similarity_deploy_exception: 基于TF-IDF的部署-异常相似度")
        print(f"- tfidf_similarity_deploy_reason: 基于TF-IDF的部署-故障原因相似度")
        print(f"- llm_correlation_reasoning: 大模型关联性分析")
        print(f"- enhanced_deploy_content: 大模型增强的部署内容")
        print(f"- enhanced_exception_semantics: 大模型增强的异常语义")

    def _enhance_with_llm_semantics(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用大模型对文本进行语义扩展和解释"""
        print("开始使用大模型进行语义扩展...")

        # 创建事件循环进行异步处理
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # 异步批处理所有文本增强任务
        results = loop.run_until_complete(
            self._batch_llm_enhance(df["deploy_content"].tolist(), df["exception_semantics"].tolist(), df["break_reason"].tolist())
        )

        df["enhanced_deploy_content"] = [r[0] for r in results]
        df["enhanced_exception_semantics"] = [r[1] for r in results]
        df["llm_correlation_reasoning"] = [r[2] for r in results]

        return df

    async def _batch_llm_enhance(self, deploy_contents: List[str], exception_semantics: List[str], break_reasons: List[str]) -> List[tuple]:
        """批量异步处理LLM语义增强"""
        tasks = []

        for deploy_content, exception_semantic, break_reason in zip(deploy_contents, exception_semantics, break_reasons):
            task = self._async_llm_process(deploy_content, exception_semantic, break_reason)
            tasks.append(task)

        # 控制并发数以避免API限制
        semaphore = asyncio.Semaphore(10)  # 最多同时处理10个请求

        async def limited_task(task):
            async with semaphore:
                return await task

        limited_tasks = [limited_task(task) for task in tasks]

        # 分批处理，每批间隔1秒
        batch_size = 30
        results = []

        for i in range(0, len(limited_tasks), batch_size):
            batch = limited_tasks[i : i + batch_size]
            batch_results = await asyncio.gather(*batch, return_exceptions=True)
            results.extend(batch_results)

            # 批次间延迟
            if i + batch_size < len(limited_tasks):
                await asyncio.sleep(1)

        return results

    async def _async_llm_process(self, deploy_content: str, exception_semantic: str, break_reason: str) -> tuple:
        """异步处理单个LLM任务"""
        # 检查缓存
        cache_key = hashlib.md5(f"{deploy_content}|{exception_semantic}|{break_reason}".encode()).hexdigest()
        cached_result = self._get_llm_cache(cache_key)
        if cached_result is not None:
            return cached_result

        # 并行执行三个LLM任务
        enhanced_deploy_task = asyncio.create_task(self._async_llm_enhance_deploy_content(deploy_content))
        enhanced_exception_task = asyncio.create_task(self._async_llm_enhance_exception_semantics(exception_semantic))
        correlation_task = asyncio.create_task(self._async_llm_analyze_correlation(deploy_content, exception_semantic, break_reason))

        try:
            enhanced_deploy = await enhanced_deploy_task
            enhanced_exception = await enhanced_exception_task
            correlation = await correlation_task

            result = (enhanced_deploy, enhanced_exception, correlation)
            # 缓存结果
            self._set_llm_cache(cache_key, result)

            return result
        except Exception as e:
            print(f"LLM processing error: {e}")
            # 出错时返回原始内容
            return (deploy_content, exception_semantic, "")

    @lru_cache(maxsize=1000)
    def _llm_enhance_deploy_content(self, deploy_content: str) -> str:
        """使用大模型扩展部署内容的语义"""
        if not deploy_content.strip():
            return deploy_content

        prompt = f"""
        作为ECS运维专家，请分析以下部署内容，提取关键信息并进行语义扩展：

        部署内容：{deploy_content}

        请从以下角度进行分析：
        1. 涉及的系统组件和服务
        2. 操作类型（重启、安装、配置等）
        3. 可能的影响范围
        4. 风险等级评估

        请用简洁的中文总结，不超过100字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=200, top_p=0.8, temperature=0.7)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"LLM API error for deploy content: {response.status_code}")
                return deploy_content
        except Exception as e:
            print(f"LLM enhance deploy content error: {e}")
            return deploy_content

    async def _async_llm_enhance_deploy_content(self, deploy_content: str) -> str:
        """异步版本的部署内容语义扩展"""
        return self._llm_enhance_deploy_content(deploy_content)

    @lru_cache(maxsize=1000)
    def _llm_enhance_exception_semantics(self, exception_semantics: str) -> str:
        """使用大模型扩展异常语义"""
        if not exception_semantics.strip():
            return exception_semantics

        prompt = f"""
        作为ECS运维专家，请分析以下异常信息，提取关键特征并进行语义扩展：

        异常信息：{exception_semantics}

        请从以下角度进行分析：
        1. 异常类型和严重程度
        2. 可能的根本原因
        3. 影响的系统层面
        4. 典型的解决方案

        请用简洁的中文总结，不超过100字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=200, top_p=0.8, temperature=0.7)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"LLM API error for exception semantics: {response.status_code}")
                return exception_semantics
        except Exception as e:
            print(f"LLM enhance exception semantics error: {e}")
            return exception_semantics

    async def _async_llm_enhance_exception_semantics(self, exception_semantics: str) -> str:
        """异步版本的异常语义扩展"""
        return self._llm_enhance_exception_semantics(exception_semantics)

    @lru_cache(maxsize=1000)
    def _llm_analyze_correlation(self, deploy_content: str, exception_semantics: str, break_reason: str) -> str:
        """使用大模型分析部署与异常的关联性"""
        if not deploy_content.strip() or not exception_semantics.strip():
            return ""

        prompt = f"""
        作为ECS运维专家，请分析部署操作与异常之间的关联性：

        部署内容：{deploy_content}
        异常信息：{exception_semantics}
        故障原因：{break_reason}

        请分析：
        1. 部署操作是否可能导致该异常？(高/中/低/无关联)
        2. 关联的技术原理是什么？
        3. 置信度评估(0-10分)

        请用简洁的中文回答，不超过80字：
        """

        try:
            response = ds.Generation.call(model="qwen-turbo", prompt=prompt, max_tokens=150, top_p=0.8, temperature=0.7)
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"LLM API error for correlation analysis: {response.status_code}")
                return ""
        except Exception as e:
            print(f"LLM analyze correlation error: {e}")
            return ""

    async def _async_llm_analyze_correlation(self, deploy_content: str, exception_semantics: str, break_reason: str) -> str:
        """异步版本的关联性分析"""
        return self._llm_analyze_correlation(deploy_content, exception_semantics, break_reason)

    @lru_cache(maxsize=2000)
    def _get_llm_cache(self, key: str) -> Optional[tuple]:
        """获取LLM结果缓存"""
        return None  # 实际实现可以连接Redis或其他缓存系统

    def _set_llm_cache(self, key: str, value: tuple) -> None:
        """设置LLM结果缓存"""
        pass  # 实际实现可以连接Redis或其他缓存系统

    def _is_cache_valid(self, cache_path: str, ttl_hours: int = 24) -> bool:
        """检查缓存文件是否有效"""
        if not os.path.exists(cache_path):
            return False

        # 检查文件修改时间
        file_mtime = os.path.getmtime(cache_path)
        current_time = time.time()
        file_age_hours = (current_time - file_mtime) / 3600

        return file_age_hours < ttl_hours

    def _compute_embedding_similarity(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用text-embedding计算高质量语义相似度"""

        # 获取文本embedding
        print("获取deploy_content的embedding...")
        deploy_embeddings = self._get_batch_embeddings(df["enhanced_deploy_content"].tolist())

        print("获取exception_semantics的embedding...")
        exception_embeddings = self._get_batch_embeddings(df["enhanced_exception_semantics"].tolist())

        print("获取break_reason的embedding...")
        break_reason_embeddings = self._get_batch_embeddings(df["break_reason"].tolist())

        # 计算语义相似度
        similarity_deploy_exception = []
        similarity_deploy_reason = []
        similarity_exception_reason = []

        for i in range(len(df)):
            # 计算deploy_content和exception_semantics的相似度
            sim_de = self._cosine_similarity(deploy_embeddings[i], exception_embeddings[i])
            similarity_deploy_exception.append(sim_de)

            # 计算deploy_content和break_reason的相似度
            sim_dr = self._cosine_similarity(deploy_embeddings[i], break_reason_embeddings[i])
            similarity_deploy_reason.append(sim_dr)

            # 计算exception_semantics和break_reason的相似度
            sim_er = self._cosine_similarity(exception_embeddings[i], break_reason_embeddings[i])
            similarity_exception_reason.append(sim_er)

        df["embedding_similarity_deploy_exception"] = similarity_deploy_exception
        df["embedding_similarity_deploy_reason"] = similarity_deploy_reason
        df["embedding_similarity_exception_reason"] = similarity_exception_reason

        return df

    def _get_batch_embeddings(self, texts: List[str], batch_size: int = 50) -> List[np.ndarray]:
        """批量获取文本embedding，优化批处理大小和延迟控制"""
        embeddings = []

        # 过滤空文本并记录索引
        valid_texts = []
        valid_indices = []
        empty_embedding = np.zeros(1024)

        for i, text in enumerate(texts):
            if text and text.strip():
                valid_texts.append(text)
                valid_indices.append(i)
            else:
                # 为每个位置预设零向量
                embeddings.append(empty_embedding)

        # 处理有效文本
        for i in range(0, len(valid_texts), batch_size):
            batch_texts = valid_texts[i : i + batch_size]
            batch_embeddings = []

            # 批量获取embedding
            batch_response = self._get_batch_embedding_api(batch_texts)

            if batch_response is not None:
                batch_embeddings = batch_response
            else:
                # 如果批量获取失败，则逐个获取
                for text in batch_texts:
                    embedding = self._get_single_embedding(text)
                    batch_embeddings.append(embedding)

            embeddings.extend(batch_embeddings)

            # 动态调整延迟：成功时减少延迟，失败时增加延迟
            if batch_response is not None:
                time.sleep(0.05)  # 成功时短延迟
            else:
                time.sleep(0.2)  # 失败时长延迟

        return embeddings

    def _get_batch_embedding_api(self, texts: List[str]) -> Optional[List[np.ndarray]]:
        """批量获取文本embedding API调用"""
        if not texts:
            return []

        try:
            response = ds.TextEmbedding.call(model="text-embedding-v4", input=texts, parameters={"output_type": "embedding"})

            if response.status_code == 200:
                embeddings = []
                for item in response.output["embeddings"]:
                    embeddings.append(np.array(item["embedding"]))
                return embeddings
            else:
                print(f"Batch embedding API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Batch embedding error: {e}")
            return None

    @lru_cache(maxsize=5000)
    def _get_single_embedding(self, text: str) -> np.ndarray:
        """获取单个文本的embedding，添加缓存机制"""
        if not text.strip():
            return np.zeros(1024)  # text-embedding-v4的向量维度

        # 重试机制
        for attempt in range(3):
            try:
                response = ds.TextEmbedding.call(model="text-embedding-v4", input=text, parameters={"output_type": "embedding"})

                if response.status_code == 200:
                    return np.array(response.output["embeddings"][0]["embedding"])
                else:
                    print(f"Embedding API error: {response.status_code}, attempt {attempt + 1}")
                    if attempt < 2:  # 不是最后一次尝试
                        time.sleep(0.1 * (attempt + 1))  # 指数退避
                    continue
            except Exception as e:
                print(f"Embedding error on attempt {attempt + 1}: {e}")
                if attempt < 2:  # 不是最后一次尝试
                    time.sleep(0.1 * (attempt + 1))  # 指数退避
                continue

        # 所有尝试都失败后返回零向量
        print(f"All attempts failed for text embedding, returning zero vector")
        return np.zeros(1024)

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        if np.linalg.norm(vec1) == 0 or np.linalg.norm(vec2) == 0:
            return 0.0
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

    def _compute_tfidf_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算TF-IDF特征作为备选"""

        # 使用TF-IDF向量化文本
        vectorizer = TfidfVectorizer(max_features=1000, stop_words="english", ngram_range=(1, 2))

        # 合并所有文本进行向量化
        all_texts = list(df["enhanced_deploy_content"]) + list(df["enhanced_exception_semantics"]) + list(df["break_reason"])
        all_texts = [text for text in all_texts if text.strip()]

        if len(all_texts) > 0:
            # 训练向量化器
            vectorizer.fit(all_texts)

            # 向量化各个文本字段
            deploy_content_vec = vectorizer.transform(df["enhanced_deploy_content"])
            exception_semantics_vec = vectorizer.transform(df["enhanced_exception_semantics"])
            break_reason_vec = vectorizer.transform(df["break_reason"])

            # 计算TF-IDF相似度
            tfidf_similarity_deploy_exception = []
            tfidf_similarity_deploy_reason = []

            for i in range(len(df)):
                # 计算deploy_content和exception_semantics的TF-IDF相似度
                sim_de = cosine_similarity(deploy_content_vec[i], exception_semantics_vec[i])[0][0]
                tfidf_similarity_deploy_exception.append(sim_de)

                # 计算deploy_content和break_reason的TF-IDF相似度
                sim_dr = cosine_similarity(deploy_content_vec[i], break_reason_vec[i])[0][0]
                tfidf_similarity_deploy_reason.append(sim_dr)

            df["tfidf_similarity_deploy_exception"] = tfidf_similarity_deploy_exception
            df["tfidf_similarity_deploy_reason"] = tfidf_similarity_deploy_reason
        else:
            df["tfidf_similarity_deploy_exception"] = 0.0
            df["tfidf_similarity_deploy_reason"] = 0.0

        return df


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="特征工程处理脚本")
    parser.add_argument("--skip-download", action="store_true", help="跳过数据下载步骤")
    parser.add_argument("--use-cache", action="store_true", help="优先使用缓存数据")
    parser.add_argument("--output-path", type=str, default=None, help="输出文件路径")

    args = parser.parse_args()

    # 初始化特征工程器
    feature = FeatureEngineer()

    # 下载数据
    if not args.skip_download:
        sql = "SELECT * FROM ecs_dw.ecs_deploy_key_metric_clean_noise"
        feature.download({"sql_query": sql, "cache_ttl_hours": 24})

    # 特征工程
    df = feature.generate_feature()

    if df is not None:
        # 保存特征
        feature.save_features(df, output_path=args.output_path)
        print("特征工程处理完成!")
    else:
        print("特征工程处理失败!")
