#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import dashscope as ds
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import time


class LLMSemanticTester:
    """测试大模型语义特征生成功能"""
    
    def __init__(self):
        pass
    
    def _llm_enhance_deploy_content(self, deploy_content: str) -> str:
        """使用大模型扩展部署内容的语义"""
        if not deploy_content.strip():
            return deploy_content
            
        prompt = f"""
        作为ECS运维专家，请分析以下部署内容，提取关键信息并进行语义扩展：

        部署内容：{deploy_content}

        请从以下角度进行分析：
        1. 涉及的系统组件和服务
        2. 操作类型（重启、安装、配置等）
        3. 可能的影响范围
        4. 风险等级评估

        请用简洁的中文总结，不超过100字：
        """
        
        try:
            response = ds.Generation.call(
                model="qwen-turbo",
                prompt=prompt,
                max_tokens=200
            )
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"API error: {response.status_code}")
                return deploy_content
        except Exception as e:
            print(f"LLM enhance deploy content error: {e}")
            return deploy_content
    
    def _llm_enhance_exception_semantics(self, exception_semantics: str) -> str:
        """使用大模型扩展异常语义"""
        if not exception_semantics.strip():
            return exception_semantics
            
        prompt = f"""
        作为ECS运维专家，请分析以下异常信息，提取关键特征并进行语义扩展：

        异常信息：{exception_semantics}

        请从以下角度进行分析：
        1. 异常类型和严重程度
        2. 可能的根本原因
        3. 影响的系统层面
        4. 典型的解决方案

        请用简洁的中文总结，不超过100字：
        """
        
        try:
            response = ds.Generation.call(
                model="qwen-turbo", 
                prompt=prompt,
                max_tokens=200
            )
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"API error: {response.status_code}")
                return exception_semantics
        except Exception as e:
            print(f"LLM enhance exception semantics error: {e}")
            return exception_semantics
    
    def _llm_analyze_correlation(self, deploy_content: str, exception_semantics: str, break_reason: str) -> str:
        """使用大模型分析部署与异常的关联性"""
        if not deploy_content.strip() or not exception_semantics.strip():
            return ""
            
        prompt = f"""
        作为ECS运维专家，请分析部署操作与异常之间的关联性：

        部署内容：{deploy_content}
        异常信息：{exception_semantics}
        故障原因：{break_reason}

        请分析：
        1. 部署操作是否可能导致该异常？(高/中/低/无关联)
        2. 关联的技术原理是什么？
        3. 置信度评估(0-10分)

        请用简洁的中文回答，不超过80字：
        """
        
        try:
            response = ds.Generation.call(
                model="qwen-turbo",
                prompt=prompt, 
                max_tokens=150
            )
            if response.status_code == 200:
                return response.output["text"].strip()
            else:
                print(f"API error: {response.status_code}")
                return ""
        except Exception as e:
            print(f"LLM analyze correlation error: {e}")
            return ""
    
    def _get_single_embedding(self, text: str) -> np.ndarray:
        """获取单个文本的embedding"""
        if not text.strip():
            return np.zeros(1024)  # text-embedding-v4的向量维度
            
        try:
            response = ds.TextEmbedding.call(
                model="text-embedding-v4",
                input=text
            )
            if response.status_code == 200:
                return np.array(response.output["embeddings"][0]["embedding"])
            else:
                print(f"Embedding API error: {response.status_code}")
                return np.zeros(1024)
        except Exception as e:
            print(f"Embedding error: {e}")
            return np.zeros(1024)
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        if np.linalg.norm(vec1) == 0 or np.linalg.norm(vec2) == 0:
            return 0.0
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    
    def test_llm_features(self):
        """测试大模型语义特征"""
        print("=== 测试大模型语义特征生成 ===\n")
        
        # 测试数据
        test_cases = [
            {
                "deploy_content": "restart nc service; install rpm:agent-hook; configure libvirt settings",
                "exception_name": "nc_unavailable",
                "xdc_component": "nc",
                "xdc_subcomponent": "service",
                "tag_name": "critical",
                "break_reason": "nc service restart failed due to dependency issues"
            },
            {
                "deploy_content": "configure network bridge settings; restart network service",
                "exception_name": "network_timeout",
                "xdc_component": "network",
                "xdc_subcomponent": "bridge",
                "tag_name": "warning",
                "break_reason": "network configuration caused connectivity issues"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"--- 测试案例 {i} ---")
            print(f"原始部署内容: {case['deploy_content']}")
            
            # 构建异常语义描述
            exception_semantics = f"异常名称: {case['exception_name']} 部件: {case['xdc_component']} 子部件: {case['xdc_subcomponent']} 标签: {case['tag_name']}"
            print(f"异常语义描述: {exception_semantics}")
            print(f"故障原因: {case['break_reason']}")
            
            print("\n1. 大模型语义扩展:")
            # 测试部署内容扩展
            enhanced_deploy = self._llm_enhance_deploy_content(case['deploy_content'])
            print(f"   增强后部署内容: {enhanced_deploy}")
            
            # 测试异常语义扩展
            enhanced_exception = self._llm_enhance_exception_semantics(exception_semantics)
            print(f"   增强后异常语义: {enhanced_exception}")
            
            # 测试关联性分析
            correlation_reasoning = self._llm_analyze_correlation(
                case['deploy_content'], exception_semantics, case['break_reason']
            )
            print(f"   关联性分析: {correlation_reasoning}")
            
            print("\n2. Text-Embedding 相似度计算:")
            # 获取embeddings
            deploy_embedding = self._get_single_embedding(enhanced_deploy)
            exception_embedding = self._get_single_embedding(enhanced_exception)
            reason_embedding = self._get_single_embedding(case['break_reason'])
            
            # 计算相似度
            sim_deploy_exception = self._cosine_similarity(deploy_embedding, exception_embedding)
            sim_deploy_reason = self._cosine_similarity(deploy_embedding, reason_embedding)
            sim_exception_reason = self._cosine_similarity(exception_embedding, reason_embedding)
            
            print(f"   部署-异常相似度: {sim_deploy_exception:.4f}")
            print(f"   部署-故障原因相似度: {sim_deploy_reason:.4f}")
            print(f"   异常-故障原因相似度: {sim_exception_reason:.4f}")
            
            print("\n3. TF-IDF 对比:")
            # TF-IDF相似度对比
            texts = [enhanced_deploy, enhanced_exception, case['break_reason']]
            vectorizer = TfidfVectorizer(max_features=100, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform(texts)
            
            tfidf_sim_deploy_exception = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            tfidf_sim_deploy_reason = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[2:3])[0][0]
            
            print(f"   TF-IDF部署-异常相似度: {tfidf_sim_deploy_exception:.4f}")
            print(f"   TF-IDF部署-故障原因相似度: {tfidf_sim_deploy_reason:.4f}")
            
            print("\n" + "="*60 + "\n")
            
            # 添加延时避免API限制
            time.sleep(1)
        
        print("✅ 大模型语义特征测试完成！")
        print("\n主要改进：")
        print("1. 使用大模型对部署内容和异常语义进行智能扩展")
        print("2. 使用text-embedding获取高质量语义向量")
        print("3. 计算多维度语义相似度（部署-异常、部署-故障原因、异常-故障原因）")
        print("4. 提供大模型的关联性分析和推理")
        print("5. 保留TF-IDF作为对比基准")


if __name__ == "__main__":
    tester = LLMSemanticTester()
    tester.test_llm_features()
