# 发布-异常相关性预测模型

## 项目概述
基于 ECS 发布和异常数据，通过高级特征工程和机器学习技术，构建发布和异常因果关系预测模型。

## 项目结构

```
change_correlation/
├── src/                           # 源代码目录
│   ├── feature.py                # 特征工程核心模块
│   ├── correlation_predictor.py  # 相关性预测器
│   ├── evaluation.py             # 模型评估模块
│   └── process.py                # 数据处理脚本
├── models/                        # 模型文件目录
│   └── correlation_predictor.pkl # 训练好的预测模型
├── experiments/                   # 实验和分析脚本
│   ├── case_analysis.py          # 好/坏案例分析
│   └── visualize_results.py      # 结果可视化
├── tests/                         # 测试文件目录
│   ├── test_feature_engineering.py  # 特征工程测试
│   └── test_llm_features.py      # 大模型特征测试
├── docs/                          # 文档目录
│   └── README.md                  # 详细技术文档
├── reports/                       # 报告目录
│   └── case_analysis_report.md   # 案例分析报告
└── README.md                      # 项目说明文件
```

## 快速开始

### 1. 环境设置
```bash
pip install pandas numpy scikit-learn dashscope
```

### 2. 特征工程
```python
from src.feature import FeatureEngineer

engineer = FeatureEngineer()
enhanced_data = engineer.generate_feature()
```

### 3. 模型训练
```python
from src.correlation_predictor import CorrelationPredictor

predictor = CorrelationPredictor()
predictor.train_and_save(data_path)
```

### 4. 预测使用
```python
from src.correlation_predictor import CorrelationPredictor

predictor = CorrelationPredictor()
predictor.load_model('models/correlation_predictor.pkl')
result = predictor.predict_single(features_dict)
```

## 模型性能
- **AUC**: 0.9985
- **准确率**: 97.3%
- **交叉验证 AUC**: 0.9983 ± 0.0021

## 主要特性
1. **高级特征工程**: 结合时间、统计、语义、结构化多种特征
2. **大模型增强**: 使用 LLM 进行语义扩展和相似度计算
3. **业务可解释性**: 提供详细的特征重要性分析
4. **案例分析**: 支持好/坏案例的详细分析

## 文档
- 详细技术文档: [docs/README.md](docs/README.md)
- 案例分析报告: [reports/case_analysis_report.md](reports/case_analysis_report.md)
