# 发布-异常相关性预测模型 评估报告

## 项目概述

本项目基于 ECS 发布和异常数据，通过高级特征工程和机器学习技术，构建了一个能够预测发布和异常是否存在因果关系的模型。

## 数据概览

- **总样本数**: 2,031
- **原始特征数**: 34
- **增强后特征数**: 67 (新增 33 个特征)
- **正样本比例**: 83.0% (相关)
- **负样本比例**: 17.0% (不相关)

## 特征工程

### 1. 时间序列特征

- `is_pre_release_anomaly`: 异常是否发生在发布之前 (强负向信号)
- `time_of_day_deploy`: 发布时间段分类
- `day_of_week_deploy`: 发布星期
- `deploy_exception_abs_duration`: 发布到异常的绝对时间差

### 2. 统计与比例特征

- `release_hit_rate`: 发布任务异常命中率
- `exception_noise_rate`: 异常背景噪音率
- `anomaly_density_on_family`: 机型族异常密度
- `exception_severity_score`: 异常严重程度得分

### 3. 文本语义特征

- `feature_similarity_deploy_exception`: 发布内容与异常语义相似度
- `feature_similarity_deploy_reason`: 发布内容与熔断原因相似度
- `deploy_content_length`: 发布内容长度
- `exception_semantics_length`: 异常语义描述长度

### 4. 结构化特征

- `restarted_services_count`: 重启服务数量
- `installed_rpms_count`: 安装 RPM 数量
- `involves_*_service`: 涉及特定服务的布尔特征
- `involves_*_rpm`: 涉及特定 RPM 的布尔特征

## 标签创建策略

采用多层次业务逻辑创建标签：

1. **强正样本**: `break_reasonable_flag = 1` 且 `break_impact_flag = 1`
2. **强负样本**: `is_pre_release_anomaly = True` (异常发生在发布之前)
3. **语义正样本**: 高语义相似度 + 短时间窗口 + 高发布命中率
4. **噪音负样本**: 高噪音率 + 低发布命中率

## 模型性能

### 传统机器学习模型对比

| 模型                | AUC        | 准确率     | 交叉验证 AUC        |
| ------------------- | ---------- | ---------- | ------------------- |
| **Random Forest**   | **0.9985** | **0.9730** | **0.9983 ± 0.0021** |
| Gradient Boosting   | 0.9937     | 0.9681     | 0.9950 ± 0.0059     |
| Logistic Regression | 0.9420     | 0.9214     | 0.9403 ± 0.0124     |
| SVM                 | 0.8494     | 0.8968     | 0.8115 ± 0.0509     |

### 最佳模型 (Random Forest) 详细评估

**混淆矩阵**:

```
                预测
实际     不相关  相关
不相关    59     10
相关       1    337
```

**分类报告**:

```
              precision    recall  f1-score   support
不相关           0.98      0.86      0.91        69
相关             0.97      1.00      0.98       338
accuracy                             0.97       407
macro avg        0.98      0.93      0.95       407
weighted avg     0.97      0.97      0.97       407
```

## 特征重要性分析

### Top 15 关键特征

| 特征                                  | 重要性 | 说明               |
| ------------------------------------- | ------ | ------------------ |
| `deploy_nc_count`                     | 0.1396 | 发布规模           |
| `exception_noise_rate`                | 0.1102 | 异常背景噪音       |
| `deploy_exception_nc_count`           | 0.0896 | 发布后异常数量     |
| `exception_nc_count`                  | 0.0673 | 异常总数量         |
| `release_hit_rate`                    | 0.0658 | 发布命中率         |
| `deploy_hour`                         | 0.0458 | 发布时间           |
| `exception_keymetric_nc_count`        | 0.0445 | 关联发布的异常数量 |
| `exception_severity_score`            | 0.0393 | 异常严重程度       |
| `deploy_exception_abs_duration`       | 0.0372 | 时间间隔           |
| `feature_similarity_deploy_exception` | 0.0371 | 语义相似度         |
| `installed_rpms_count`                | 0.0367 | 安装 RPM 数量      |
| `exception_duration_minutes`          | 0.0350 | 异常持续时间       |
| `day_of_week_deploy`                  | 0.0321 | 发布星期           |
| `deploy_exception_duration`           | 0.0282 | 发布到异常时间差   |
| `deploy_content_length`               | 0.0280 | 发布内容长度       |

## 业务洞察

1. **发布规模是最重要的预测因子**: `deploy_nc_count` 特征重要性最高，说明发布规模对相关性判断至关重要。

2. **异常噪音率能有效区分真实关联**: `exception_noise_rate` 排名第二，证明了背景噪音分析的有效性。

3. **语义相似度特征贡献显著**: `feature_similarity_deploy_exception` 进入 Top 10，验证了文本语义分析的价值。

4. **时间特征揭示发布模式**: `deploy_hour` 和 `day_of_week_deploy` 都有重要贡献，说明发布时间模式对风险评估有意义。

5. **结构化特征增强预测能力**: `installed_rpms_count` 等结构化特征的重要性说明了从非结构化文本提取结构化信息的价值。

## 模型应用

### 使用场景

1. **实时风险评估**: 发布后实时评估异常与发布的关联性
2. **熔断决策支持**: 为熔断决策提供量化依据
3. **发布风险预警**: 基于历史模式预测发布风险
4. **运维效率提升**: 减少误判，提高运维响应效率

### 预测示例

```python
# 示例特征
sample_features = {
    'deploy_exception_abs_duration': 30,  # 发布后30分钟出现异常
    'release_hit_rate': 0.1,  # 发布命中率10%
    'exception_noise_rate': 0.2,  # 异常噪音率20%
    'feature_similarity_deploy_exception': 0.4,  # 语义相似度40%
    # ... 其他特征
}

# 预测结果
# 预测结果: 相关
# 置信度: 0.680
# 相关概率: 0.680
```

## 模型优势

1. **高准确性**: AUC 达到 0.9985，准确率 97.3%
2. **业务可解释性**: 特征重要性分析提供了清晰的业务洞察
3. **多层次特征工程**: 结合时间、统计、语义、结构化多种特征
4. **鲁棒性**: 通过交叉验证证明了模型的稳定性
5. **可扩展性**: 支持新特征的添加和模型的持续优化

## 文件结构

```
algorithm/
├── change_correlation/
│   ├── feature.py          # 特征工程类
│   ├── evaluation.py       # 模型评估类
│   └── process.py          # 数据处理脚本
├── data/
│   └── ecs_deploy_key_metric_clean_noise/
│       ├── feature.csv     # 原始数据
│       └── features_enhanced.csv  # 增强特征数据
├── correlation_predictor.py  # 预测器类
├── visualize_results.py     # 结果可视化
└── model_evaluation_results.png  # 评估结果图
```

## 后续优化建议

1. **深度学习模型**: 尝试使用深度学习模型进一步提升性能
2. **在线学习**: 实现模型的在线更新机制
3. **更多语义特征**: 集成更先进的文本嵌入模型
4. **时序建模**: 加入时间序列建模提升预测准确性
5. **多目标优化**: 考虑成本敏感学习，优化不同类型错误的权重

---

**模型版本**: v1.0  
**评估时间**: 2025 年 7 月 18 日  
**性能指标**: AUC=0.9985, Accuracy=0.9730
