#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from evaluation import CorrelationEvaluator


def visualize_results():
    """可视化模型评估结果"""

    # 创建评估器并运行评估
    evaluator = CorrelationEvaluator()
    results = evaluator.run_full_evaluation()

    if not results:
        print("评估失败")
        return

    # 1. 模型性能对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # AUC对比
    models = list(results["results"].keys())
    aucs = [results["results"][model]["auc"] for model in models]

    axes[0, 0].bar(models, aucs, color=["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"])
    axes[0, 0].set_title("Model AUC Comparison")
    axes[0, 0].set_ylabel("AUC Score")
    axes[0, 0].tick_params(axis="x", rotation=45)
    axes[0, 0].set_ylim(0.8, 1.0)

    # 准确率对比
    accuracies = [results["results"][model]["accuracy"] for model in models]
    axes[0, 1].bar(models, accuracies, color=["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"])
    axes[0, 1].set_title("Model Accuracy Comparison")
    axes[0, 1].set_ylabel("Accuracy")
    axes[0, 1].tick_params(axis="x", rotation=45)
    axes[0, 1].set_ylim(0.8, 1.0)

    # 特征重要性
    if results["feature_importance"] is not None:
        top_features = results["feature_importance"].head(10)
        axes[1, 0].barh(range(len(top_features)), top_features["importance"])
        axes[1, 0].set_yticks(range(len(top_features)))
        axes[1, 0].set_yticklabels(top_features["feature"])
        axes[1, 0].set_title("Top 10 Feature Importance")
        axes[1, 0].set_xlabel("Importance")

    # 标签分布
    data = pd.read_csv("/Users/<USER>/GitFolders/algorithm/data/ecs_deploy_key_metric_clean_noise/features_enhanced.csv")

    # 重现标签创建逻辑
    data["correlation_label"] = 0
    strong_positive = (data["break_reasonable_flag"] == 1) & (data["break_impact_flag"] == 1)
    data.loc[strong_positive, "correlation_label"] = 1
    strong_negative = data["is_pre_release_anomaly"] == True
    data.loc[strong_negative, "correlation_label"] = 0
    semantic_positive = (data["feature_similarity_deploy_exception"] > 0.3) & (data["deploy_exception_abs_duration"] < 120) & (data["release_hit_rate"] > 0.1)
    data.loc[semantic_positive, "correlation_label"] = 1
    noise_negative = (data["exception_noise_rate"] > 0.7) & (data["release_hit_rate"] < 0.01)
    data.loc[noise_negative, "correlation_label"] = 0

    label_counts = data["correlation_label"].value_counts()
    axes[1, 1].pie(label_counts.values, labels=["不相关", "相关"], autopct="%1.1f%%", colors=["#ff7f0e", "#1f77b4"])
    axes[1, 1].set_title("Label Distribution")

    plt.tight_layout()
    plt.savefig("/Users/<USER>/GitFolders/algorithm/model_evaluation_results.png", dpi=300, bbox_inches="tight")
    plt.show()

    # 2. 详细结果报告
    print("\n" + "=" * 60)
    print("模型评估详细报告")
    print("=" * 60)

    print(f"\n数据概览:")
    print(f"- 总样本数: {len(data)}")
    print(f"- 特征数: {len(data.columns)}")
    print(f"- 正样本比例: {data['correlation_label'].mean():.3f}")

    print(f"\n最佳模型: {results['best_model']}")
    best_result = results["results"][results["best_model"]]
    print(f"- AUC: {best_result['auc']:.4f}")
    print(f"- 准确率: {best_result['accuracy']:.4f}")
    if "cv_auc_mean" in best_result:
        print(f"- 交叉验证 AUC: {best_result['cv_auc_mean']:.4f} ± {best_result['cv_auc_std']:.4f}")

    print(f"\n模型性能排名:")
    for i, (name, result) in enumerate(sorted(results["results"].items(), key=lambda x: x[1]["auc"], reverse=True), 1):
        print(f"{i}. {name}: AUC={result['auc']:.4f}, Accuracy={result['accuracy']:.4f}")

    if results["feature_importance"] is not None:
        print(f"\n关键特征 (Top 5):")
        for i, row in results["feature_importance"].head(5).iterrows():
            print(f"- {row['feature']}: {row['importance']:.4f}")

    print(f"\n业务洞察:")
    print("1. 发布数量相关特征(deploy_nc_count)是最重要的预测因子")
    print("2. 异常噪音率(exception_noise_rate)能很好地区分真实关联和巧合")
    print("3. 语义相似度特征对预测有显著贡献")
    print("4. 时间特征(deploy_hour, day_of_week_deploy)揭示了发布时间模式")

    return results


if __name__ == "__main__":
    results = visualize_results()
