#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import warnings

warnings.filterwarnings("ignore")


class CaseAnalyzer:
    """Good Case 和 Bad Case 分析器"""

    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.model = None
        self.X_test = None
        self.y_test = None
        self.y_pred = None
        self.y_pred_proba = None

    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("加载和准备数据...")
        self.data = pd.read_csv(self.data_path)

        # 创建标签
        self.data["correlation_label"] = 0
        strong_positive = (self.data["break_reasonable_flag"] == 1) & (self.data["break_impact_flag"] == 1)
        self.data.loc[strong_positive, "correlation_label"] = 1
        strong_negative = self.data["is_pre_release_anomaly"] == True
        self.data.loc[strong_negative, "correlation_label"] = 0
        semantic_positive = (
            (self.data["feature_similarity_deploy_exception"] > 0.3)
            & (self.data["deploy_exception_abs_duration"] < 120)
            & (self.data["release_hit_rate"] > 0.1)
        )
        self.data.loc[semantic_positive, "correlation_label"] = 1
        noise_negative = (self.data["exception_noise_rate"] > 0.7) & (self.data["release_hit_rate"] < 0.01)
        self.data.loc[noise_negative, "correlation_label"] = 0

        # 选择特征
        feature_names = [
            "deploy_exception_abs_duration",
            "deploy_hour",
            "day_of_week_deploy",
            "is_weekend_deploy",
            "exception_duration_minutes",
            "release_hit_rate",
            "exception_noise_rate",
            "anomaly_density_on_family",
            "exception_severity_score",
            "is_long_duration_exception",
            "feature_similarity_deploy_exception",
            "feature_similarity_deploy_reason",
            "deploy_content_length",
            "exception_semantics_length",
            "restarted_services_count",
            "installed_rpms_count",
            "involves_nc_service",
            "involves_agent_service",
            "involves_kvm_service",
            "involves_network_service",
            "involves_storage_service",
            "involves_houyi_rpm",
            "involves_agent-hook_rpm",
            "involves_kvm-config_rpm",
            "involves_libvirt_rpm",
            "is_performance_exception",
            "is_unavailable_exception",
            "is_local_disk_bool",
            "duration",
            "deploy_exception_duration",
            "deploy_exception_nc_count",
            "deploy_nc_count",
            "exception_nc_count",
            "exception_keymetric_nc_count",
        ]

        # 过滤可用特征
        available_features = [col for col in feature_names if col in self.data.columns]
        X = self.data[available_features].copy()

        # 处理分类特征
        categorical_features = ["time_of_day_deploy"]
        for col in categorical_features:
            if col in X.columns:
                le = LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))

        # 处理缺失值
        X = X.fillna(X.mean())
        y = self.data["correlation_label"]

        # 划分数据集
        X_train, self.X_test, y_train, self.y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

        # 训练模型
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)

        # 预测测试集
        self.y_pred = self.model.predict(self.X_test)
        self.y_pred_proba = self.model.predict_proba(self.X_test)[:, 1]

        # 获取测试集对应的原始数据
        self.test_indices = self.X_test.index
        self.test_data = self.data.loc[self.test_indices].copy()
        self.test_data["predicted_label"] = self.y_pred
        self.test_data["predicted_proba"] = self.y_pred_proba
        self.test_data["actual_label"] = self.y_test.values

        return self.test_data

    def analyze_cases(self):
        """分析不同类型的case"""
        if self.test_data is None:
            self.load_and_prepare_data()

        # 分类不同的case
        true_positives = self.test_data[(self.test_data["actual_label"] == 1) & (self.test_data["predicted_label"] == 1)]
        true_negatives = self.test_data[(self.test_data["actual_label"] == 0) & (self.test_data["predicted_label"] == 0)]
        false_positives = self.test_data[(self.test_data["actual_label"] == 0) & (self.test_data["predicted_label"] == 1)]
        false_negatives = self.test_data[(self.test_data["actual_label"] == 1) & (self.test_data["predicted_label"] == 0)]

        print(f"测试集统计:")
        print(f"- 总样本数: {len(self.test_data)}")
        print(f"- True Positives (正确预测为相关): {len(true_positives)}")
        print(f"- True Negatives (正确预测为不相关): {len(true_negatives)}")
        print(f"- False Positives (误判为相关): {len(false_positives)}")
        print(f"- False Negatives (误判为不相关): {len(false_negatives)}")

        return {"true_positives": true_positives, "true_negatives": true_negatives, "false_positives": false_positives, "false_negatives": false_negatives}

    def show_case_examples(self, case_type, data, num_examples=10, title=""):
        """展示具体的case例子"""
        print(f"\n{'='*80}")
        print(f"{title}")
        print(f"{'='*80}")

        if len(data) == 0:
            print("没有找到相应的案例")
            return

        # 按照预测概率排序
        if case_type in ["true_positives", "false_positives"]:
            # 对于预测为正的case，按概率从高到低排序
            sample_data = data.nlargest(num_examples, "predicted_proba")
        else:
            # 对于预测为负的case，按概率从低到高排序
            sample_data = data.nsmallest(num_examples, "predicted_proba")

        for i, (idx, row) in enumerate(sample_data.iterrows(), 1):
            print(f"\n--- Case {i} ---")
            print(f"实例ID: {row['instance_id']}")
            print(f"异常名称: {row['exception_name']}")
            print(f"发布服务: {row['deploy_service']}")
            print(f"发布内容: {row['deploy_content'][:100]}...")
            print(f"熔断原因: {row['break_reason']}")
            print(f"熔断评论: {str(row['break_comment'])[:100]}...")

            print(f"\n关键特征:")
            print(f"- 发布到异常时间差: {row['deploy_exception_abs_duration']} 分钟")
            print(f"- 发布命中率: {row['release_hit_rate']:.4f}")
            print(f"- 异常噪音率: {row['exception_noise_rate']:.4f}")
            print(f"- 语义相似度: {row['feature_similarity_deploy_exception']:.4f}")
            print(f"- 发布时间: {row['deploy_time']} (第{row['deploy_hour']}时)")
            print(f"- 异常持续时间: {row['exception_duration_minutes']:.1f} 分钟")
            print(f"- 发布规模: 发布{row['deploy_nc_count']}台，异常{row['deploy_exception_nc_count']}台")

            print(f"\n预测结果:")
            print(f"- 实际标签: {'相关' if row['actual_label'] == 1 else '不相关'}")
            print(f"- 预测标签: {'相关' if row['predicted_label'] == 1 else '不相关'}")
            print(f"- 预测概率: {row['predicted_proba']:.4f}")

            # 业务判断
            business_logic = self.explain_business_logic(row)
            print(f"\n业务逻辑分析:")
            for logic in business_logic:
                print(f"- {logic}")

    def explain_business_logic(self, row):
        """解释业务逻辑"""
        explanations = []

        # 时间相关性
        if row["deploy_exception_abs_duration"] < 60:
            explanations.append(f"时间高度相关：异常在发布后{row['deploy_exception_abs_duration']}分钟内发生")
        elif row["deploy_exception_abs_duration"] > 720:  # 12小时
            explanations.append(f"时间相关性弱：异常在发布后{row['deploy_exception_abs_duration']}分钟才发生")

        # 发布命中率
        if row["release_hit_rate"] > 0.1:
            explanations.append(f"发布命中率高：{row['release_hit_rate']:.2%}的发布机器出现此异常")
        elif row["release_hit_rate"] < 0.01:
            explanations.append(f"发布命中率低：仅{row['release_hit_rate']:.2%}的发布机器出现此异常")

        # 异常噪音率
        if row["exception_noise_rate"] > 0.5:
            explanations.append(f"异常噪音率高：{row['exception_noise_rate']:.2%}可能为背景噪音")
        elif row["exception_noise_rate"] < 0.2:
            explanations.append(f"异常噪音率低：{row['exception_noise_rate']:.2%}背景噪音较少")

        # 语义相似度
        if row["feature_similarity_deploy_exception"] > 0.3:
            explanations.append(f"语义相关性高：发布内容与异常描述相似度{row['feature_similarity_deploy_exception']:.2%}")

        # 熔断标记
        if row["break_reasonable_flag"] == 1 and row["break_impact_flag"] == 1:
            explanations.append("人工确认：熔断合理且有业务影响")
        elif row["break_reasonable_flag"] == 0:
            explanations.append("人工确认：熔断不合理")

        # 发布前异常
        if row["is_pre_release_anomaly"]:
            explanations.append("逻辑矛盾：异常发生在发布之前")

        return explanations if explanations else ["无明显业务逻辑特征"]

    def run_analysis(self):
        """运行完整的案例分析"""
        print("开始案例分析...")

        # 准备数据
        self.load_and_prepare_data()

        # 分析不同类型的case
        cases = self.analyze_cases()

        # 展示Good Cases (True Positives) - 正确识别为相关
        self.show_case_examples("true_positives", cases["true_positives"], num_examples=10, title="Good Cases - 正确识别为相关的案例 (True Positives)")

        # 展示Good Cases (True Negatives) - 正确识别为不相关
        self.show_case_examples("true_negatives", cases["true_negatives"], num_examples=10, title="Good Cases - 正确识别为不相关的案例 (True Negatives)")

        # 展示Bad Cases (False Positives) - 误判为相关
        self.show_case_examples("false_positives", cases["false_positives"], num_examples=5, title="Bad Cases - 误判为相关的案例 (False Positives)")

        # 展示Bad Cases (False Negatives) - 误判为不相关
        self.show_case_examples("false_negatives", cases["false_negatives"], num_examples=5, title="Bad Cases - 误判为不相关的案例 (False Negatives)")

        return cases


if __name__ == "__main__":
    # 数据路径
    data_path = "/Users/<USER>/GitFolders/algorithm/data/ecs_deploy_key_metric_clean_noise/features_enhanced.csv"

    # 创建分析器
    analyzer = CaseAnalyzer(data_path)

    # 运行分析
    cases = analyzer.run_analysis()
