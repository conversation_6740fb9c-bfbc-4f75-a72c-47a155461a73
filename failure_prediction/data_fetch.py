import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from connector.download import ODPSDownloader


def get_sql(main_date):
    """Generate SQL query with parameterized dates.

    Args:
        main_date (str): Main date in format 'YYYY-MM-DD'

    Returns:
        str: SQL query with parameterized dates
    """
    # Calculate relative dates
    from datetime import datetime, timedelta

    main_date_obj = datetime.strptime(main_date, "%Y-%m-%d")
    prev_day_1 = (main_date_obj - timedelta(days=1)).strftime("%Y%m%d")
    prev_day_2 = (main_date_obj - timedelta(days=2)).strftime("%Y%m%d")
    main_date_ymd = main_date_obj.strftime("%Y%m%d")

    return f"""WITH failure_events AS (
    SELECT 
        vm_name,
        tp,
        survive_duration,
        notify_duration,
        failure_down_duration,
        rule_name,
        reason,
        nc_ip
    FROM    ecs_dw.dw_cloudops_event_vm_survive_duration_all
    WHERE   ds = MAX_PT('ecs_dw.dw_cloudops_event_vm_survive_duration_all')
      AND   tp >= '{main_date} 00:00:00'
      AND   tp <= '{main_date} 23:59:59'
      AND   is_test_user = 'false'
      AND   is_gamma = 'false'
)

SELECT * FROM (
    
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{prev_day_2}'
        UNION ALL
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{prev_day_1}'
        UNION ALL
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{main_date_ymd}'
    ) a
    ON a.instanceid = f.vm_name
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp

    UNION ALL

    
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{prev_day_2}'
        UNION ALL
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{prev_day_1}'
        UNION ALL
        SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{main_date_ymd}'
    ) a
    ON a.ncip = f.nc_ip
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp
) t;
""".format(
        prev_day_2=prev_day_2, prev_day_1=prev_day_1, main_date_ymd=main_date_ymd
    )


def get_sql_short_downtime(start_date, end_date):
    """Generate SQL query for downtime events with survive_duration <= 4 hours.
    
    Args:
        start_date (str): Start date in format 'YYYY-MM-DD'
        end_date (str): End date in format 'YYYY-MM-DD'
        
    Returns:
        str: SQL query for short downtime events
    """
    # For short downtime events, we can use a longer date range
    # Calculate date range for alert data (include a few days before start_date)
    from datetime import datetime, timedelta
    
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Include 5 days before start_date for alert data
    alert_start_date = (start_date_obj - timedelta(days=5)).strftime("%Y%m%d")
    alert_end_date = end_date_obj.strftime("%Y%m%d")
    
    return f"""-- 开启 ODPS2 类型系统（必须放在最前面）

-- Step 1: 提取 {start_date} 到 {end_date} 的所有宕机事件（生存时间<=4小时）
WITH failure_events AS (
    SELECT 
        vm_name,
        tp,
        survive_duration,
        notify_duration,
        failure_down_duration,
        rule_name,
        reason,
        nc_ip
    FROM    ecs_dw.dw_cloudops_event_vm_survive_duration_all
    WHERE   ds = MAX_PT('ecs_dw.dw_cloudops_event_vm_survive_duration_all')
      AND   tp >= '{start_date} 00:00:00'
      AND   tp <= '{end_date} 23:59:59'
      AND   is_test_user = 'false'
      AND   is_gamma = 'false'
      AND   survive_duration <= 4
)
select * from (
-- 主查询：分别匹配 instanceid 和 ncip，再 UNION ALL
SELECT * FROM (
    -- 分支1：匹配虚拟机自身异常（instanceid = vm_name）
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert 
        WHERE ds >= '{alert_start_date}' AND ds <= '{alert_end_date}'
    ) a
    ON a.instanceid = f.vm_name
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp

    UNION ALL

    -- 分支2：匹配宿主机异常（ncip = nc_ip）
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        SELECT * FROM ecs_dw.monitor_exception_sls_alert 
        WHERE ds >= '{alert_start_date}' AND ds <= '{alert_end_date}'
    ) a
    ON a.ncip = f.nc_ip
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp
) t);
"""


def main():
    """Main function to download data using ODPSDownloader."""
    import argparse
    from datetime import datetime
    import time

    # 创建参数解析器
    parser = argparse.ArgumentParser(description="Download failure prediction data from ODPS")
    parser.add_argument(
        "--date",
        type=str,
        help="Main date for data download in format YYYY-MM-DD",
        default=datetime.now().strftime("%Y-%m-%d"),
    )
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date for short downtime data download in format YYYY-MM-DD",
    )
    parser.add_argument(
        "--end-date",
        type=str,
        help="End date for short downtime data download in format YYYY-MM-DD",
    )
    parser.add_argument("--output-dir", type=str, help="Output directory", default="./data")
    parser.add_argument("--output-filename", type=str, help="Output filename")
    parser.add_argument("--chunk-size", type=int, help="Chunk size for download", default=20000)
    parser.add_argument("--timeout", type=int, help="Timeout in seconds", default=3600)  # 5分钟默认超时

    # 解析参数
    args = parser.parse_args()

    # 检查是否提供了短时宕机数据下载的日期范围
    if args.start_date and args.end_date:
        # 如果提供了开始和结束日期，则使用短时宕机数据下载模式
        # 生成相应的输出文件名
        if not args.output_filename:
            start_date_for_filename = args.start_date.replace("-", "")
            end_date_for_filename = args.end_date.replace("-", "")
            args.output_filename = f"failure_prediction_data_short_downtime_{start_date_for_filename}_to_{end_date_for_filename}.csv"
        
        # 获取短时宕机数据的SQL
        sql_query = get_sql_short_downtime(args.start_date, args.end_date)
    else:
        # 如果没有提供开始和结束日期，则使用原有的单日数据下载模式
        # 如果没有指定输出文件名，则以日期命名
        if not args.output_filename:
            # 将日期格式从 YYYY-MM-DD 转换为 YYYYMMDD
            date_for_filename = args.date.replace("-", "")
            args.output_filename = f"failure_prediction_data_{date_for_filename}.csv"

        # 获取参数化的SQL
        sql_query = get_sql(args.date)

    # 配置ODPSDownloader
    # 注意：这里需要根据实际情况填写access_id, access_key等信息
    config = {
        "access_id": "LTAI5tHSnP1rfc5Nne9TtHPL",
        "access_key": "******************************",
        "project": "ecs_dw",
        "endpoint": "http://service-corp.odps.aliyun-inc.com/api",
        "chunk_size": args.chunk_size,
        "progress_file": "download_progress.json",
    }

    # 使用ODPSDownloader下载数据
    downloader = ODPSDownloader(config)
    print(f"Starting download with timeout of {args.timeout} seconds...")
    start_time = time.time()

    try:
        downloader.download_with_custom_params(
            sql_query=sql_query,
            output_dir=args.output_dir,
            output_filename=args.output_filename,
            chunk_size=args.chunk_size,
        )
        end_time = time.time()
        print(f"Download completed in {end_time - start_time:.2f} seconds.")
    except Exception as e:
        end_time = time.time()
        print(f"Download failed after {end_time - start_time:.2f} seconds with error: {e}")
        raise


if __name__ == "__main__":
    main()
