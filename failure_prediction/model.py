"""
故障预测模型 - 简化版本
基于序列处理的统一架构，使用平均池化 + MLP分类器
"""

import os
import gzip
import json
import numpy as np
from collections import defaultdict
import random
from datetime import datetime, timedelta

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

import pytorch_lightning as pl
from pytorch_lightning.callbacks import EarlyStopping, ModelCheckpoint, Callback

from torchmetrics.classification import (
    BinaryAccuracy,
    BinaryPrecision,
    BinaryRecall,
    BinaryF1Score,
    BinaryConfusionMatrix,
    BinaryAUROC,
)

from sklearn.preprocessing import StandardScaler

# SwanLab 实验跟踪
try:
    import swanlab
    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False
    print("⚠️  SwanLab不可用，将使用本地日志")

from rich.console import Console

console = Console()



class FailurePredictionConfig:
    """故障预测配置类 - 统一使用序列处理模式"""

    def __init__(self):
        """初始化配置参数"""
        # 使用os包管理相对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)  # 获取项目根目录

        # 构建相对路径
        self.data_path = os.path.join(
            project_root,
            "failure_prediction",
            "processed",
            "processed_new_data_20250927_214625.json.gz",
        )
        self.output_dir = os.path.join(project_root, "failure_prediction", "models")
        self.logs_dir = os.path.join(project_root, "failure_prediction", "logs")

        # 确保路径存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)

        # 验证数据文件是否存在
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")

        self.random_seed = 42

        # 数据参数
        self.val_size = 0.15
        self.train_size = 0.7
        self.test_size = 1 - self.train_size - self.val_size

        # 序列处理参数
        self.max_seq_length = 100  # 最大序列长度
        self.min_seq_length = 2  # 最小序列长度
        self.feature_dim = 16  # 单个时间步的特征维度（不含时间戳）

        # 时间过滤参数
        self.filter_tp_before_hours = 1.0  # 过滤TP前1小时内的记录（设置为None禁用）
        self.use_prediction_mode = True  # 是否使用预测模式（过滤TP时刻后的记录）

        # 模型参数 - 使用简化的分类器架构
        self.hidden_dim = 128
        self.dropout_rate = 0.3

        # 训练参数
        self.batch_size = 32
        self.learning_rate = 0.01
        self.max_epochs = 100
        self.patience = 10
        self.weight_decay = 1e-4

        # 类别不平衡处理
        self.use_class_weights = True
        self.pos_weight_scale = 13.5  # 正样本权重倍数

        # 设备
        self.accelerator = "gpu" if torch.cuda.is_available() else "cpu"
        self.devices = [0] if torch.cuda.is_available() else "auto"


class FailurePredictionDataset(Dataset):
    """故障预测数据集 - 基于序列处理"""

    def __init__(self, data, config, feature_scaler=None):
        self.data = data
        self.config = config
        self.feature_scaler = feature_scaler

        # 使用序列模式的预处理逻辑
        self._preprocess_sequence_data()

    def _parse_timestamp(self, timestamp_str):
        """解析时间戳（从sequence_model.py复制）"""
        try:
            # 尝试不同的时间格式
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            # 如果都失败，返回None
            return None
        except:
            return None

    def _preprocess_sequence_data(self):
        """序列模式数据预处理（复杂模式）"""
        sequences = []
        labels = []
        time_diffs_list = []
        lengths = []

        for item in self.data:
            # 获取特征列表
            feature_list = item["feature_list"]
            if not feature_list or len(feature_list) < self.config.min_seq_length:
                continue

            try:
                # 解析时间戳并排序
                timestamped_features = []
                for i, feature in enumerate(feature_list):
                    if len(feature) > 0:
                        timestamp_str = feature[0]  # 时间戳在第一个位置
                        timestamp = self._parse_timestamp(str(timestamp_str))
                        if timestamp:
                            timestamped_features.append((timestamp, feature, i))

                if len(timestamped_features) < self.config.min_seq_length:
                    continue

                # 按时间排序
                timestamped_features.sort(key=lambda x: x[0])

                # 获取TP时间（group_key[3]）
                tp_timestamp_str = item["group_key"][3]
                tp_timestamp = self._parse_timestamp(str(tp_timestamp_str))

                if tp_timestamp is None:
                    # 只抽样输出TP时间戳解析错误（每100个样本输出一次）
                    if len(sequences) % 100 == 0:
                        console.print(
                            f"[yellow]⚠️  无法解析TP时间戳: {tp_timestamp_str}, 跳过此样本[/yellow]"
                        )
                    continue

                # 过滤记录
                filtered_features = []
                if (
                    self.config.use_prediction_mode
                    and self.config.filter_tp_before_hours is not None
                ):
                    # 计算过滤时间窗口
                    filter_before_time = tp_timestamp - timedelta(
                        hours=self.config.filter_tp_before_hours
                    )

                    # 只保留在过滤时间之前的记录
                    for timestamp, feature, idx in timestamped_features:
                        if timestamp < filter_before_time:
                            filtered_features.append((timestamp, feature, idx))

                    # 只在异常情况下且抽样输出（过滤掉太多记录）
                    if len(filtered_features) < len(timestamped_features) * 0.5 and len(sequences) % 50 == 0:
                        console.print(
                            f"[yellow]⚠️  过滤掉大量记录: 原始{len(timestamped_features)} -> 过滤后{len(filtered_features)}[/yellow]"
                        )
                else:
                    filtered_features = timestamped_features

                if len(filtered_features) < self.config.min_seq_length:
                    # 只抽样输出严重异常情况（每100个样本输出一次）
                    if len(filtered_features) < self.config.min_seq_length // 2 and len(sequences) % 100 == 0:
                        console.print(
                            f"[yellow]⚠️  过滤后记录数严重不足: {len(filtered_features)}, 跳过此样本[/yellow]"
                        )
                    continue

                # 提取特征和计算相对时间 - 按相邻时间去重
                base_time = filtered_features[0][0]
                seq_features = []
                time_diffs = []

                # 数据已经预处理去重，直接使用过滤后的特征（简化处理）
                processed_features = filtered_features[:self.config.max_seq_length]  # 限制最大长度

                # 处理特征
                for timestamp, feature, _ in processed_features:
                    try:
                        # 计算相对时间差（分钟）
                        time_diff = (timestamp - base_time).total_seconds() / 60.0
                        time_diffs.append(time_diff)

                        # 处理特征 - 使用哈希编码分类特征
                        feature_vals = []
                        for val in feature[1:]:  # 跳过时间戳
                            try:
                                val = float(val) if val is not None else 0.0
                                if not np.isfinite(val):
                                    val = 0.0
                                feature_vals.append(val)
                            except (ValueError, TypeError):
                                feature_vals.append(0.0)

                        # 补齐特征维度
                        while len(feature_vals) < self.config.feature_dim:
                            feature_vals.append(0.0)

                        feature_vals = feature_vals[: self.config.feature_dim]
                        seq_features.append(feature_vals)

                    except Exception as e:
                        # 只在特定情况下输出异常（减少输出量）
                        if "invalid literal" in str(e) or "cannot convert" in str(e):
                            console.print(f"[yellow]⚠️  特征解析错误: {e}[/yellow]")
                        continue

                if len(seq_features) < self.config.min_seq_length:
                    continue

                # 转换为numpy数组
                seq_features = np.array(seq_features, dtype=np.float32)
                time_diffs = np.array(time_diffs, dtype=np.float32)

                # 特征标准化
                if self.feature_scaler is None:
                    self.feature_scaler = StandardScaler()
                    seq_features = self.feature_scaler.fit_transform(seq_features)
                else:
                    seq_features = self.feature_scaler.transform(seq_features)

                sequences.append(seq_features)
                time_diffs_list.append(time_diffs)
                lengths.append(len(seq_features))

                # 处理标签
                original_label = item["label_list"][0]
                new_label = 1 if original_label <= 4 else 0
                labels.append(new_label)

            except Exception as e:
                # 只在严重错误时输出（减少输出量）
                if "KeyError" in str(e) or "IndexError" in str(e):
                    console.print(f"[yellow]⚠️  序列处理严重错误: {e}[/yellow]")
                continue

        self.sequences = sequences
        self.features = None  # 序列模式下不使用单特征
        self.labels = np.array(labels, dtype=np.int32)
        self.time_diffs = time_diffs_list
        self.lengths = np.array(lengths, dtype=np.int32)

        console.print(f"✅ 处理了 {len(sequences)} 个有效序列")

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        sequence = torch.FloatTensor(self.sequences[idx])
        time_diffs = torch.FloatTensor(self.time_diffs[idx])
        label = torch.FloatTensor([self.labels[idx]])
        length = torch.LongTensor([self.lengths[idx]])

        return {
            "sequence": sequence,
            "time_diffs": time_diffs,
            "label": label,
            "length": length,
        }


class FailurePredictionModel(pl.LightningModule):
    """故障预测模型 - 基于序列处理的简化分类器"""

    def __init__(self, config):
        super(FailurePredictionModel, self).__init__()
        self.save_hyperparameters(config.__dict__)

        self.config = config

        # 构建简化的分类器
        self.classifier = nn.Sequential(
            nn.Linear(config.feature_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.hidden_dim // 2, 1),
        )

        # 损失函数
        if config.use_class_weights:
            pos_weight = torch.tensor([config.pos_weight_scale])
            self.criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        else:
            self.criterion = nn.BCEWithLogitsLoss()

        # 评估指标
        self._setup_metrics()



    def _setup_metrics(self):
        """设置评估指标"""
        self.train_accuracy = BinaryAccuracy()
        self.train_precision = BinaryPrecision()
        self.train_recall = BinaryRecall()
        self.train_f1 = BinaryF1Score()
        self.train_auc = BinaryAUROC()

        self.val_accuracy = BinaryAccuracy()
        self.val_precision = BinaryPrecision()
        self.val_recall = BinaryRecall()
        self.val_f1 = BinaryF1Score()
        self.val_auc = BinaryAUROC()

        self.test_accuracy = BinaryAccuracy()
        self.test_precision = BinaryPrecision()
        self.test_recall = BinaryRecall()
        self.test_f1 = BinaryF1Score()
        self.test_auc = BinaryAUROC()
        self.test_confusion_matrix = BinaryConfusionMatrix()

    def forward(self, sequences, time_diffs=None, lengths=None):
        """前向传播 - 基于序列的平均池化分类"""
        seq_len = sequences.size(1)

        # 使用长度信息进行掩码平均池化
        if lengths is not None and lengths.min() > 0:
            lengths = lengths.to(sequences.device)
            mask = torch.arange(seq_len, device=sequences.device).unsqueeze(
                0
            ) < lengths.unsqueeze(1)
            mask = mask.unsqueeze(-1).float()
            masked_sequences = sequences * mask
            pooled_features = masked_sequences.sum(dim=1) / lengths.unsqueeze(1).float()
        else:
            pooled_features = sequences.mean(dim=1)

        # 分类器
        logits = self.classifier(pooled_features)
        return logits.squeeze(-1)

    def training_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat).squeeze()
        preds = (probs > 0.5).int()

        # 更新指标
        self.train_accuracy.update(preds, labels.squeeze().int())
        self.train_precision.update(preds, labels.squeeze().int())
        self.train_recall.update(preds, labels.squeeze().int())
        self.train_f1.update(preds, labels.squeeze().int())
        self.train_auc.update(probs, labels.squeeze().int())

        self.log("train_loss", loss, prog_bar=True)
        return loss

    def on_train_epoch_end(self):
        # 记录训练指标
        self.log("train_accuracy", self.train_accuracy.compute())
        self.log("train_precision", self.train_precision.compute())
        self.log("train_recall", self.train_recall.compute())
        self.log("train_f1", self.train_f1.compute())
        self.log("train_auc", self.train_auc.compute())

        # 重置指标
        self.train_accuracy.reset()
        self.train_precision.reset()
        self.train_recall.reset()
        self.train_f1.reset()
        self.train_auc.reset()

    def validation_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat).squeeze()
        preds = (probs > 0.5).int()

        # 更新指标
        self.val_accuracy.update(preds, labels.squeeze().int())
        self.val_precision.update(preds, labels.squeeze().int())
        self.val_recall.update(preds, labels.squeeze().int())
        self.val_f1.update(preds, labels.squeeze().int())
        self.val_auc.update(probs, labels.squeeze().int())

        self.log("val_loss", loss, prog_bar=True)
        return loss

    def on_validation_epoch_end(self):
        # 记录验证指标
        self.log("val_accuracy", self.val_accuracy.compute(), prog_bar=True)
        self.log("val_precision", self.val_precision.compute())
        self.log("val_recall", self.val_recall.compute())
        self.log("val_f1", self.val_f1.compute(), prog_bar=True)
        self.log("val_auc", self.val_auc.compute())

        # 重置指标
        self.val_accuracy.reset()
        self.val_precision.reset()
        self.val_recall.reset()
        self.val_f1.reset()
        self.val_auc.reset()

    def test_step(self, batch, batch_idx):
        sequences = batch["sequence"].to(self.device)
        time_diffs = batch["time_diffs"].to(self.device)
        labels = batch["label"].to(self.device)
        lengths = batch["length"]

        y_hat = self(sequences, time_diffs, lengths)
        loss = self.criterion(y_hat, labels)

        # 计算概率用于指标计算
        probs = torch.sigmoid(y_hat).squeeze()
        preds = (probs > 0.5).int()

        # 更新指标
        self.test_accuracy.update(preds, labels.squeeze().int())
        self.test_precision.update(preds, labels.squeeze().int())
        self.test_recall.update(preds, labels.squeeze().int())
        self.test_f1.update(preds, labels.squeeze().int())
        self.test_auc.update(probs, labels.squeeze().int())
        self.test_confusion_matrix.update(preds, labels.squeeze().int())

        return loss

    def on_test_epoch_end(self):
        # 记录测试指标
        accuracy = self.test_accuracy.compute()
        precision = self.test_precision.compute()
        recall = self.test_recall.compute()
        f1 = self.test_f1.compute()
        auc = self.test_auc.compute()
        conf_matrix = self.test_confusion_matrix.compute()

        self.log("test_accuracy", accuracy)
        self.log("test_precision", precision)
        self.log("test_recall", recall)
        self.log("test_f1", f1)
        self.log("test_auc", auc)

        # 打印详细结果
        console.print("\n[bold green]🧪 测试结果详情:[/bold green]")
        console.print(f"准确率 (Accuracy): {accuracy:.4f}")
        console.print(f"精确率 (Precision): {precision:.4f}")
        console.print(f"召回率 (Recall): {recall:.4f}")
        console.print(f"F1分数 (F1-Score): {f1:.4f}")
        console.print(f"AUC得分: {auc:.4f}")

        console.print("\n[bold blue]混淆矩阵 (Confusion Matrix):[/bold blue]")
        console.print(f"{conf_matrix}")

        # 计算每个类别的详细指标
        tn, fp, fn, tp = conf_matrix.flatten()

        console.print("\n[bold cyan]详细分类指标:[/bold cyan]")
        console.print(f"真阳性 (TP): {tp}")
        console.print(f"假阳性 (FP): {fp}")
        console.print(f"真阴性 (TN): {tn}")
        console.print(f"假阴性 (FN): {fn}")

        if tp + fp > 0:
            console.print(f"阳性预测值 (PPV): {tp / (tp + fp):.4f}")
        if tp + fn > 0:
            console.print(f"真阳性率 (TPR): {tp / (tp + fn):.4f}")
        if tn + fp > 0:
            console.print(f"真阴性率 (TNR): {tn / (tn + fp):.4f}")

    def configure_optimizers(self):
        optimizer = optim.Adam(
            self.parameters(),
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay,
        )
        return optimizer


class SwanLabCallback(Callback):
    """SwanLab回调"""

    def __init__(self):
        if SWANLAB_AVAILABLE:
            self.run = swanlab.init(
                project="failure_prediction_lightning",
                experiment_name=f"lightning_experiment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

    def on_train_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "train_loss": metrics.get("train_loss", 0),
                    "train_accuracy": metrics.get("train_accuracy", 0),
                    "train_precision": metrics.get("train_precision", 0),
                    "train_recall": metrics.get("train_recall", 0),
                    "train_f1": metrics.get("train_f1", 0),
                    "train_auc": metrics.get("train_auc", 0),
                }
            )

    def on_validation_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "val_loss": metrics.get("val_loss", 0),
                    "val_accuracy": metrics.get("val_accuracy", 0),
                    "val_precision": metrics.get("val_precision", 0),
                    "val_recall": metrics.get("val_recall", 0),
                    "val_f1": metrics.get("val_f1", 0),
                    "val_auc": metrics.get("val_auc", 0),
                }
            )

    def on_test_epoch_end(self, trainer, pl_module):
        if SWANLAB_AVAILABLE:
            metrics = trainer.callback_metrics
            swanlab.log(
                {
                    "test_accuracy": metrics.get("test_accuracy", 0),
                    "test_precision": metrics.get("test_precision", 0),
                    "test_recall": metrics.get("test_recall", 0),
                    "test_f1": metrics.get("test_f1", 0),
                    "test_auc": metrics.get("test_auc", 0),
                }
            )
            swanlab.finish()


def load_and_split_data(config):
    """加载数据并按照TP分组划分"""
    console.print("[bold cyan]📖 加载数据...[/bold cyan]")

    with gzip.open(config.data_path, "rt", encoding="utf-8") as f:
        data = json.load(f)

    console.print(f"✅ 加载了 {len(data)} 个分组数据")

    # 按照TP分组
    tp_groups = defaultdict(list)
    for item in data:
        tp_value = item["group_key"][3]  # TP字段
        tp_groups[tp_value].append(item)

    # 划分TP组
    tp_list = list(tp_groups.keys())
    random.seed(config.random_seed)
    random.shuffle(tp_list)

    train_tp_count = int(len(tp_list) * config.train_size)
    val_tp_count = int(len(tp_list) * config.val_size)

    train_tp = tp_list[:train_tp_count]
    val_tp = tp_list[train_tp_count : train_tp_count + val_tp_count]
    test_tp = tp_list[train_tp_count + val_tp_count :]

    # 收集数据
    train_data = []
    val_data = []
    test_data = []

    for tp in train_tp:
        train_data.extend(tp_groups[tp])
    for tp in val_tp:
        val_data.extend(tp_groups[tp])
    for tp in test_tp:
        test_data.extend(tp_groups[tp])

    console.print("📊 数据划分完成:")
    console.print(f"  训练集: {len(train_data)} 样本")
    console.print(f"  验证集: {len(val_data)} 样本")
    console.print(f"  测试集: {len(test_data)} 样本")

    return train_data, val_data, test_data


def collate_fn(batch):
    """自定义collate函数处理变长序列"""
    sequences = []
    time_diffs = []
    labels = []
    lengths = []

    for item in batch:
        sequences.append(item["sequence"])
        time_diffs.append(item["time_diffs"])
        labels.append(item["label"])
        lengths.append(item["length"])

    # 填充序列到相同长度
    max_len = max(len(seq) for seq in sequences)
    padded_sequences = []
    padded_time_diffs = []

    for seq, time_diff in zip(sequences, time_diffs):
        # 填充序列
        pad_len = max_len - len(seq)
        if pad_len > 0:
            padding = torch.zeros(pad_len, seq.size(-1))
            padded_seq = torch.cat([seq, padding], dim=0)
            time_padding = torch.zeros(pad_len)
            padded_time = torch.cat([time_diff, time_padding], dim=0)
        else:
            padded_seq = seq
            padded_time = time_diff

        padded_sequences.append(padded_seq)
        padded_time_diffs.append(padded_time)

    return {
        "sequence": torch.stack(padded_sequences),
        "time_diffs": torch.stack(padded_time_diffs),
        "label": torch.stack(labels).unsqueeze(-1),  # [batch_size] -> [batch_size, 1]
        "length": torch.cat(lengths),
    }


def create_data_loaders(train_data, val_data, test_data, config):
    """创建数据加载器"""
    console.print("[bold cyan]🔧 创建数据集和数据加载器...[/bold cyan]")

    # 创建训练集（用于特征标准化）
    temp_dataset = FailurePredictionDataset(
        train_data[:10], config
    )  # 小样本用于初始化标准化器
    feature_scaler = temp_dataset.feature_scaler

    # 创建完整数据集
    train_dataset = FailurePredictionDataset(train_data, config, feature_scaler)
    val_dataset = FailurePredictionDataset(val_data, config, feature_scaler)
    test_dataset = FailurePredictionDataset(test_data, config, feature_scaler)

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        collate_fn=collate_fn,
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config.batch_size, collate_fn=collate_fn
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config.batch_size, collate_fn=collate_fn
    )

    # 统计数据集中的正样本比例
    def count_positive_samples(dataset):
        if hasattr(dataset, 'labels'):
            labels = dataset.labels
            total_samples = len(labels)
            positive_samples = sum(1 for label in labels if label == 1)
            positive_ratio = positive_samples / total_samples if total_samples > 0 else 0
            return total_samples, positive_samples, positive_ratio
        return 0, 0, 0

    console.print(f"📊 数据集信息:")
    console.print(f"  训练集样本数: {len(train_dataset)}")
    console.print(f"  验证集样本数: {len(val_dataset)}")
    console.print(f"  测试集样本数: {len(test_dataset)}")

    # 统计正样本比例
    console.print(f"\n📈 数据集正样本统计:")
    for dataset, name in [(train_dataset, "训练集"), (val_dataset, "验证集"), (test_dataset, "测试集")]:
        total, positive, ratio = count_positive_samples(dataset)
        console.print(f"  {name}: {positive}/{total} ({ratio:.4f})")

    return train_loader, val_loader, test_loader, feature_scaler


def train_failure_prediction_model():
    """训练故障预测模型的主函数"""
    console.print(f"[bold green]🎯 故障预测模型训练 (PyTorch Lightning)[/bold green]")
    console.print("=" * 60)

    # 配置
    config = FailurePredictionConfig()

    # 检查GPU
    console.print(f"🖥️  使用设备: {config.accelerator}")
    if config.accelerator == "gpu":
        console.print(f"   GPU型号: {torch.cuda.get_device_name(0)}")
        console.print(
            f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB"
        )

    # 设置随机种子
    pl.seed_everything(config.random_seed)

    # 加载和划分数据
    train_data, val_data, test_data = load_and_split_data(config)

    # 创建数据加载器
    train_loader, val_loader, test_loader, feature_scaler = create_data_loaders(
        train_data, val_data, test_data, config
    )

    # 创建模型
    model = FailurePredictionModel(config)

    # 显示模型信息
    console.print(f"\n[bold blue]🏗️  模型信息:[/bold blue]")
    console.print(f"  架构: 序列平均池化 + MLP分类器")
    console.print(f"  特征维度: {config.feature_dim}")
    console.print(f"  隐藏维度: {config.hidden_dim}")
    console.print(f"  最大序列长度: {config.max_seq_length}")
    console.print(f"  预测模式: {config.use_prediction_mode}")
    if config.use_prediction_mode and config.filter_tp_before_hours is not None:
        console.print(f"  TP前过滤: {config.filter_tp_before_hours} 小时")

    # 创建回调
    callbacks = [
        EarlyStopping(
            monitor="val_f1",
            patience=config.patience,
            mode="max",
            verbose=True,
        ),
        ModelCheckpoint(
            dirpath=config.output_dir,
            filename="failure_prediction-{epoch:02d}-{val_f1:.3f}",
            monitor="val_f1",
            mode="max",
            save_top_k=3,
            verbose=True,
        ),
    ]

    # 添加SwanLab回调（如果可用）
    if SWANLAB_AVAILABLE:
        callbacks.append(SwanLabCallback())

    # 创建训练器
    trainer = pl.Trainer(
        accelerator=config.accelerator,
        devices=config.devices,
        max_epochs=config.max_epochs,
        callbacks=callbacks,
        enable_progress_bar=True,
        enable_model_summary=True,
        log_every_n_steps=50,
    )

    # 训练模型
    console.print("[bold blue]⚡ 开始训练...[/bold blue]")
    trainer.fit(model, train_loader, val_loader)

    # 测试模型
    console.print("[bold blue]🧪 开始测试...[/bold blue]")
    trainer.test(model, test_loader)

    # 保存模型
    model_path = os.path.join(
        config.output_dir,
        f"failure_prediction_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth",
    )
    torch.save(
        {
            "model_state_dict": model.state_dict(),
            "config": config.__dict__,
            "feature_scaler": feature_scaler,
            "trainer_state": trainer.state_dict() if hasattr(trainer, 'state_dict') else None,
        },
        model_path,
    )

    console.print(f"[green]💾 模型已保存到: {model_path}[/green]")
    console.print("[bold blue]🎉 模型训练完成！[/bold blue]")

    # SwanLab 结束
    if SWANLAB_AVAILABLE:
        try:
            swanlab.finish()
        except RuntimeError:
            # SwanLab 可能没有被初始化，跳过
            pass

    return model, trainer


if __name__ == "__main__":
    # 运行训练
    model, trainer = train_failure_prediction_model()
