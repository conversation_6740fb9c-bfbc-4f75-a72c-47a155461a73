# %%
import os
import pandas as pd
import json
import pickle
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Confirm, Prompt

# 初始化结果存储列表
results = []

# 初始化 Rich Console
console = Console()

# 配置参数
DEBUG_MODE = False
MAX_DEBUG_FILES = 1
SAVE_RESULTS = True
SAVE_FORMAT = "json"  # "json", "pickle", "csv"

group_keys = ['vm_name', 'rule_name', 'nc_ip', 'tp']
label_columns = ['survive_duration']
feature_columns = ['exceptiontime', 'exceptionname', 'exceptiontype', 'warninglevel', 'tag_name']


# 使用os包管理相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录

# 构建相对路径
data_dir = os.path.join(project_root, "data", "failure_prediction")
output_dir = os.path.join(current_dir, "processed")

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)

# %%
# 调试模式设置
console.print("\n[bold blue]🚀 数据预处理工具[/bold blue]")
console.print("=" * 50)

if Confirm.ask("是否启用调试模式？(只处理部分文件进行测试)", default=False):
    DEBUG_MODE = True
    MAX_DEBUG_FILES = int(Prompt.ask("输入要处理的调试文件数量", default="1"))
    console.print(f"[yellow]📋 调试模式已启用，将只处理前 {MAX_DEBUG_FILES} 个文件[/yellow]")
else:
    console.print("[green]⚡ 生产模式，将处理所有文件[/green]")

# 获取所有CSV文件
all_data_files = [f for f in os.listdir(data_dir) if f.endswith(".csv")]
console.print(f"[blue]📁 发现 {len(all_data_files)} 个CSV文件:[/blue]")
for i, file in enumerate(all_data_files, 1):
    console.print(f"  {i}. {file}")

# 根据调试模式选择文件
if DEBUG_MODE and len(all_data_files) > MAX_DEBUG_FILES:
    data_files = all_data_files[:MAX_DEBUG_FILES]
    console.print(f"[yellow]🐛 调试模式：只处理前 {MAX_DEBUG_FILES} 个文件[/yellow]")
else:
    data_files = all_data_files

console.print(f"[green]✅ 最终将处理 {len(data_files)} 个文件[/green]\n")

# %%
# 文件合并功能
def merge_csv_files(file_list, data_dir):
    """合并多个CSV文件为一个DataFrame"""
    merged_df = pd.DataFrame()

    for file in file_list:
        file_path = os.path.join(data_dir, file)
        try:
            df = pd.read_csv(file_path, low_memory=False)
            df['_source_file'] = file  # 添加来源文件标记
            merged_df = pd.concat([merged_df, df], ignore_index=True)
            console.print(f"  ✅ {file}: {len(df)} 行数据")
        except Exception as e:
            console.print(f"  ❌ {file}: 读取失败 - {str(e)}")

    return merged_df

# %%
# 合并所有文件并处理
console.print("[bold cyan]🔄 正在合并所有CSV文件...[/bold cyan]")

# 创建进度条
with Progress(
    SpinnerColumn(),
    TextColumn("[bold blue]{task.description}"),
    BarColumn(),
    TaskProgressColumn(),
    TextColumn("[bold green]{task.fields[status]}"),
    TimeRemainingColumn(),
    console=console
) as progress:

    # 文件合并进度条
    merge_task = progress.add_task("合并文件", total=len(data_files), status="开始合并...")

    # 合并所有选中的文件
    merged_df = merge_csv_files(data_files, data_dir)

    progress.update(merge_task, advance=len(data_files), status=f"✅ 合并完成 ({len(merged_df)} 行总数据)")

    # 分组处理进度条
    console.print(f"[blue]📊 合并后的数据总行数: {len(merged_df)}[/blue]")
    console.print(f"[blue]📊 数据列数: {len(merged_df.columns)}[/blue]")

    # 按照 group_keys 分组
    group_task = progress.add_task("分组处理", total=1, status="正在分组...")
    df_group = merged_df.groupby(group_keys)
    total_groups = len(df_group)
    progress.update(group_task, total=total_groups, status=f"发现 {total_groups} 个分组")

    # 处理每个分组
    for group_idx, (group_key, group_df) in enumerate(df_group):
        progress.update(group_task,
                      description=f"🔍 处理分组 {group_idx+1}/{total_groups}",
                      advance=1)

        feature_list = group_df[feature_columns].values.tolist()

        # 处理 label_list：检查是否所有值都相同
        label_values = group_df[label_columns[0]].values.tolist()

        # 检查所有标签值是否相同
        if len(set(label_values)) == 1:
            # 所有值都相同，合并为单个值
            label_list = [label_values[0]]
            progress.update(group_task, status=f"✅ 合并标签 ({len(group_df)}行)")
        else:
            # 存在不同值，发出告警
            label_list = label_values
            progress.update(group_task, status=f"⚠️ 告警 ({len(set(label_values))}个值)")

        # 获取来源文件分布
        source_files = group_df['_source_file'].unique().tolist()

        # 将结果存储到列表中
        result_item = {
            "group_key": group_key,
            "feature_list": feature_list,
            "label_list": label_list,
            "source_files": source_files,
            "group_size": len(group_df),
            "total_rows": len(merged_df)
        }
        results.append(result_item)

    progress.update(group_task, status="🎉 全部处理完成!")
    progress.remove_task(merge_task)
    progress.remove_task(group_task)

# 显示美化的结果摘要
console.print(f"\n[bold green]✨ 总共处理了 {len(results)} 个分组结果[/bold green]")

# %%
# 结果保存功能
def save_results(results, output_dir, format_type="json"):
    """保存处理结果到文件"""
    import gzip

    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")

    if format_type == "json":
        # JSON格式 - 可读性好，支持压缩
        filename = f"processed_results_{timestamp}.json.gz"
        filepath = os.path.join(output_dir, filename)

        # 将tuple转换为list以支持JSON序列化
        json_results = []
        for result in results:
            json_result = result.copy()
            json_result["group_key"] = list(result["group_key"])  # tuple转list
            json_results.append(json_result)

        with gzip.open(filepath, 'wt', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)

    elif format_type == "pickle":
        # Pickle格式 - 保持完整Python对象
        filename = f"processed_results_{timestamp}.pkl"
        filepath = os.path.join(output_dir, filename)

        with open(filepath, 'wb') as f:
            pickle.dump(results, f)

    elif format_type == "csv":
        # CSV格式 - 简化版本，每个分组一行
        filename = f"processed_results_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)

        # 转换为DataFrame
        csv_data = []
        for result in results:
            row = {
                "group_key": str(result["group_key"]),
                "group_size": result["group_size"],
                "source_files": ";".join(result["source_files"]),
                "label_count": len(result["label_list"]),
                "feature_count": len(result["feature_list"]),
                "total_rows": result["total_rows"]
            }
            csv_data.append(row)

        df_csv = pd.DataFrame(csv_data)
        df_csv.to_csv(filepath, index=False)

    console.print(f"[green]💾 结果已保存到: {filepath}[/green]")
    return filepath

# %%
# 创建美化的结果摘要面板
summary_text = Text()
summary_text.append("📊 处理结果摘要\n\n", style="bold blue")

total_groups = len(results)
# 计算涉及的总文件数
all_source_files = []
for r in results:
    all_source_files.extend(r['source_files'])
total_files = len(set(all_source_files))
alert_count = sum(1 for r in results if len(set(r['label_list'])) > 1)
merged_count = total_groups - alert_count

summary_text.append(f"总分组数: {total_groups}\n")
summary_text.append(f"涉及文件数: {total_files}\n")
summary_text.append(f"标签已合并: {merged_count} 个分组\n")
summary_text.append(f"存在告警: {alert_count} 个分组\n")

if alert_count > 0:
    summary_text.append(f"\n⚠️  发现 {alert_count} 个分组存在不同标签值，请检查!", style="bold red")
else:
    summary_text.append(f"\n✅ 所有分组标签值均相同，已全部合并!", style="bold green")

summary_panel = Panel(summary_text, title="[bold]数据处理报告[/bold]", border_style="blue")
console.print(summary_panel)

# 显示前5个结果示例
if results:
    examples_text = Text()
    examples_text.append("前5个结果示例:\n\n", style="bold cyan")

    for i, result in enumerate(results[:5]):
        examples_text.append(f"{i+1}. 分组: {result['group_key']}\n", style="yellow")
        examples_text.append(f"   📁 来源文件: {', '.join(result['source_files'])}\n")
        examples_text.append(f"   📏 大小: {result['group_size']} 行\n")
        examples_text.append(f"   🔢 特征: {len(result['feature_list'])} 个\n")
        examples_text.append(f"   🏷️  标签: {result['label_list']}\n\n")

    examples_panel = Panel(examples_text, title="[bold]结果示例[/bold]", border_style="green")
    console.print(examples_panel)

console.print("[bold green]🎉 数据预处理完成！[/bold green]")

# %%
# 保存结果
if SAVE_RESULTS and results:
    console.print("\n[bold cyan]💾 正在保存处理结果...[/bold cyan]")

    # 选择保存格式
    if SAVE_FORMAT == "json":
        saved_path = save_results(results, output_dir, "json")
    elif SAVE_FORMAT == "pickle":
        saved_path = save_results(results, output_dir, "pickle")
    elif SAVE_FORMAT == "csv":
        saved_path = save_results(results, output_dir, "csv")
    else:
        console.print(f"[yellow]⚠️  未知的保存格式: {SAVE_FORMAT}，使用默认JSON格式[/yellow]")
        saved_path = save_results(results, output_dir, "json")

    # 显示保存结果信息
    file_size = os.path.getsize(saved_path) / (1024 * 1024)  # MB
    console.print(f"[green]📊 文件大小: {file_size:.2f} MB[/green]")
    console.print(f"[green]📂 保存路径: {saved_path}[/green]")
else:
    console.print("[yellow]⚠️  没有结果需要保存[/yellow]")

console.print("[bold blue]🏁 所有任务完成！[/bold blue]")
# %%
