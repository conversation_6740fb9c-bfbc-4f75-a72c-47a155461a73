import os
import pandas as pd
import json
import gzip
from datetime import datetime
import ast
import numpy as np
from collections import defaultdict
from typing import List, Dict, Any
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def parse_struct_tuple_list(feature_str: str) -> List[Dict[str, Any]]:
    """解析StructNamedTuple格式的异常列表"""
    try:
        # 移除外层引号和方括号
        if feature_str.startswith('"[') and feature_str.endswith(']"'):
            feature_str = feature_str[2:-2]
        elif feature_str.startswith('[') and feature_str.endswith(']'):
            feature_str = feature_str[1:-1]

        if not feature_str.strip():
            return []

        # 使用正则表达式解析StructNamedTuple
        import re

        # 匹配StructNamedTuple的模式
        pattern = r'StructNamedTuple\(exception_time=datetime\.datetime\(([^)]+)\),\s*exception_name=\'([^\']+)\',\s*exception_type=\'([^\']+)\',\s*warning_level=\'([^\']+)\',\s*tag_name=\'([^\']*)\'\)'

        matches = re.findall(pattern, feature_str)
        parsed_tuples = []

        for match in matches:
            try:
                # 解析datetime
                dt_parts = match[0].split(', ')
                dt = datetime(*map(int, dt_parts))

                parsed_tuples.append({
                    'exception_time': dt,
                    'exception_name': match[1],
                    'exception_type': match[2],
                    'warning_level': match[3],
                    'tag_name': match[4]
                })
            except Exception as e:
                console.print(f"[yellow]⚠️  解析单个异常失败: {e}[/yellow]")
                continue

        return parsed_tuples

    except Exception as e:
        console.print(f"[red]❌ 解析异常序列失败: {e}[/red]")
        console.print(f"[red]原始字符串: {feature_str[:200]}...[/red]")
        return []


def parse_label_list(label_str: str) -> List[int]:
    """解析标签列表"""
    try:
        if label_str.startswith('[') and label_str.endswith(']'):
            label_str = label_str[1:-1]
        if not label_str.strip():
            return []

        labels = []
        for item in label_str.split(','):
            item = item.strip()
            if item:
                try:
                    labels.append(int(float(item)))
                except ValueError:
                    continue
        return labels
    except Exception as e:
        console.print(f"[red]❌ 解析标签失败: {e}[/red]")
        return []


def convert_to_failure_format(row) -> Dict[str, Any]:
    """将CSV行转换为故障预测格式"""
    try:
        # 解析异常序列
        exceptions = parse_struct_tuple_list(row['feature_list'])
        if not exceptions:
            return None

        # 解析标签
        labels = parse_label_list(row['label_list'])
        if not labels:
            return None

        # 构建特征列表 - 每个异常作为一个时间步
        feature_list = []
        for exc in exceptions:
            try:
                # 时间戳转换为字符串
                timestamp = exc['exception_time']
                if isinstance(timestamp, datetime):
                    timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp_str = str(timestamp)

                # 构建特征向量：时间戳 + 异常名编码
                feature = [
                    timestamp_str,  # 时间戳
                    exc['exception_name'],  # 异常名
                    exc['exception_type'],  # 异常类型
                    exc['warning_level'],  # 警告级别
                    exc['tag_name']  # 标签名
                ]
                feature_list.append(feature)

            except Exception as e:
                console.print(f"[yellow]⚠️  处理异常时出错: {e}[/yellow]")
                continue

        if not feature_list:
            return None

        # 解析TP时间
        try:
            tp_timestamp = datetime.strptime(row['tp'], '%Y-%m-%d %H:%M:%S')
            tp_str = tp_timestamp.strftime('%Y-%m-%d %H:%M:%S')
        except:
            console.print(f"[yellow]⚠️  无法解析TP时间: {row['tp']}[/yellow]")
            return None

        # 构建group_key
        group_key = [
            row['vm_name'],  # VM名称
            row['rule_name'],  # 规则名称
            row['nc_ip'],  # NC IP
            tp_str  # TP时间
        ]

        return {
            "group_key": group_key,
            "feature_list": feature_list,
            "label_list": labels
        }

    except Exception as e:
        console.print(f"[red]❌  转换行数据失败: {e}[/red]")
        return None


def load_csv_files(data_dir: str) -> List[Dict[str, Any]]:
    """加载所有CSV文件并转换为统一格式"""
    all_data = []

    # 遍历所有日期目录
    date_dirs = [d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d)) and d != '__pycache__']

    with Progress() as progress:
        task = progress.add_task("加载CSV文件", total=len(date_dirs))

        for date_dir in date_dirs:
            date_path = os.path.join(data_dir, date_dir)

            # 处理final_*.csv文件（正样本）
            final_file = os.path.join(date_path, f"final_{date_dir}.csv")
            if os.path.exists(final_file):
                try:
                    df = pd.read_csv(final_file)
                    console.print(f"[blue]📖 加载 {final_file}, {len(df)} 行[/blue]")

                    for _, row in df.iterrows():
                        converted = convert_to_failure_format(row)
                        if converted:
                            all_data.append(converted)

                except Exception as e:
                    console.print(f"[red]❌ 加载 {final_file} 失败: {e}[/red]")

            # 处理false_label_*.csv文件（负样本）
            false_file = os.path.join(date_path, f"false_label_{date_dir}.csv")
            if os.path.exists(false_file):
                try:
                    df = pd.read_csv(false_file)
                    console.print(f"[blue]📖 加载 {false_file}, {len(df)} 行[/blue]")

                    for _, row in df.iterrows():
                        converted = convert_to_failure_format(row)
                        if converted:
                            all_data.append(converted)

                except Exception as e:
                    console.print(f"[red]❌ 加载 {false_file} 失败: {e}[/red]")

            progress.update(task, advance=1)

    console.print(f"[green]✅ 总共加载了 {len(all_data)} 个有效样本[/green]")
    return all_data


def save_processed_data(data: List[Dict[str, Any]], output_path: str):
    """保存处理后的数据"""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    with gzip.open(output_path, 'wt', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    console.print(f"[green]💾 数据已保存到: {output_path}[/green]")


def main():
    """主函数"""
    console.print("[bold green]🔄 开始预处理新格式的故障预测数据[/bold green]")

    # 数据目录
    data_dir = os.path.join(os.path.dirname(__file__), "..", "data", "failure_prediction")

    if not os.path.exists(data_dir):
        console.print(f"[red]❌ 数据目录不存在: {data_dir}[/red]")
        return

    # 输出路径
    output_dir = os.path.join(os.path.dirname(__file__), "processed")
    os.makedirs(output_dir, exist_ok=True)

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = os.path.join(output_dir, f"processed_new_data_{timestamp}.json.gz")

    # 加载和转换数据
    console.print("[bold cyan]📖 加载CSV数据...[/bold cyan]")
    data = load_csv_files(data_dir)

    if not data:
        console.print("[red]❌ 没有加载到有效数据[/red]")
        return

    # 保存数据
    console.print("[bold cyan]💾 保存处理后的数据...[/bold cyan]")
    save_processed_data(data, output_path)

    # 显示统计信息
    console.print("[bold blue]📊 数据统计:[/bold blue]")
    console.print(f"  总样本数: {len(data)}")

    # 统计标签分布
    label_counts = defaultdict(int)
    for item in data:
        for label in item['label_list']:
            label_counts[label] += 1

    console.print("  标签分布:")
    for label, count in sorted(label_counts.items()):
        console.print(f"    标签 {label}: {count} 个样本")

    console.print("[bold green]🎉 数据预处理完成！[/bold green]")


if __name__ == "__main__":
    main()
