import sys
import os

# 添加项目根目录到Python路径
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_PATH not in sys.path:
    sys.path.append(BASE_PATH)

from connector.download import ODPSDownloader
import argparse
from datetime import datetime, timedelta

# 原始SQL查询
SQL_TEMPLATE = """set odps.sql.select.output.showcolumntype=true;
SET odps.sql.type.system.odps2=true;
SET odps.sql.datetime.type=timestamp;
set odps.task.sql.sqa.enable=false;
WITH failure_events AS (
    SELECT 
        vm_name,
        tp,
        survive_duration,
        notify_duration,
        failure_down_duration,
        rule_name,
        reason,
        nc_ip
    FROM    ecs_dw.dw_cloudops_event_vm_survive_duration_all
    WHERE   ds = MAX_PT('ecs_dw.dw_cloudops_event_vm_survive_duration_all')
      AND   tp >= '{start_time}'
      AND   tp <= '{end_time}'
      AND   is_test_user = 'false'
      AND   is_gamma = 'false'
)

SELECT * FROM (
    
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        {union_queries}
    ) a
    ON a.instanceid = f.vm_name
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp

    UNION ALL

    
    SELECT 
        f.vm_name,
        f.tp,
        f.survive_duration,
        f.notify_duration,
        f.failure_down_duration,
        f.rule_name,
        f.reason,
        f.nc_ip,
        a.log_time,
        a.exceptiontime,
        a.gmtstarted,
        a.gmtcreated,
        a.instanceid,
        a.aliuid,
        a.realhostname,
        a.hostname,
        a.sn,
        a.machine_id,
        a.ncip,
        a.nc_id,
        a.vpcinstanceid,
        a.vswitchinstanceid,
        a.status,
        a.ecsbusinessstatus,
        a.state,
        a.exceptionname,
        a.exceptiontype,
        a.warninglevel,
        a.lock_type,
        a.lock_reason,
        a.biz_status,
        a.instancetype,
        a.cores,
        a.islocaldisk,
        a.virt_type,
        a.cpu_generation,
        a.physical_model,
        a.model,
        a.storage_type,
        a.storage_network_type,
        a.region,
        a.iz,
        a.idc,
        a.room,
        a.rack,
        a.asw_id,
        a.cluster_alias,
        a.networktype,
        a.network_arch,
        a.app_group,
        a.product_name,
        a.tag_name,
        a.istestaccount,
        a.is_gamma_machine,
        a.isfullnc,
        a.salerate,
        a.usercnt,
        a.ostype,
        a.osname,
        a.imagename,
        a.os_release_version,
        a.internetip,
        a.intranetip,
        a.internetrx,
        a.internettx,
        a.intranetrx,
        a.intranettx,
        a.supportautorecovery,
        a.parent_service_tag
    FROM    failure_events f
    JOIN (
        {union_queries}
    ) a
    ON a.ncip = f.nc_ip
    WHERE 
        a.exceptiontime >= DATEADD(TO_DATE(f.tp, 'yyyy-mm-dd hh:mi:ss'), -2, 'dd')
        AND a.exceptiontime <= f.tp
) t;
"""


def generate_union_queries(date_str):
    """生成UNION查询语句"""
    # 生成三天的日期（包括前一天和后一天）
    target_date = datetime.strptime(date_str, "%Y%m%d")
    dates = []
    for i in range(-1, 2):  # 前一天、当天、后一天
        date = target_date + timedelta(days=i)
        dates.append(date.strftime("%Y%m%d"))
    
    union_queries = []
    for date in dates:
        union_queries.append(f"SELECT * FROM ecs_dw.monitor_exception_sls_alert WHERE ds = '{date}'")
    
    return "\n        UNION ALL\n        ".join(union_queries)


# 示例配置
config = {
    "access_id": "LTAI5tHSnP1rfc5Nne9TtHPL",
    "access_key": "******************************",
    "project": "ecs_dw",
    "endpoint": "http://service-corp.odps.aliyun-inc.com/api",
    "chunk_size": 10000,
    "progress_file": "download_progress.json",
}


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download failure prediction data from ODPS")
    parser.add_argument("--date", type=str, required=True, help="Date for data download in format YYYYMMDD")
    parser.add_argument("--start-time", type=str, help="Start time for query in format 'YYYY-MM-DD HH:MM:SS'")
    parser.add_argument("--end-time", type=str, help="End time for query in format 'YYYY-MM-DD HH:MM:SS'")
    parser.add_argument("--output-dir", type=str, help="Output directory", 
                        default="./data")
    parser.add_argument("--output-filename", type=str, help="Output filename")
    parser.add_argument("--chunk-size", type=int, help="Chunk size for download", default=10000)
    
    args = parser.parse_args()

    # 如果没有提供开始和结束时间，则默认为指定日期的全天
    if not args.start_time:
        args.start_time = f"{args.date[:4]}-{args.date[4:6]}-{args.date[6:8]} 00:00:00"
    if not args.end_time:
        args.end_time = f"{args.date[:4]}-{args.date[4:6]}-{args.date[6:8]} 23:59:59"
    if not args.output_filename:
        args.output_filename = f"failure_prediction_data_{args.date}.csv"

    # 生成UNION查询
    union_queries = generate_union_queries(args.date)
    
    # 格式化SQL查询
    sql_query = SQL_TEMPLATE.format(
        start_time=args.start_time,
        end_time=args.end_time,
        union_queries=union_queries
    )

    # 使用现有的ODPSDownloader
    downloader = ODPSDownloader(config)
    downloader.download_with_custom_params(
        sql_query=sql_query,
        output_dir=args.output_dir,
        output_filename=args.output_filename,
        chunk_size=args.chunk_size,
    )