#!/usr/bin/env python3
"""
SwanLab集成训练示例
展示如何使用新的配置功能和SwanLab实验跟踪
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def example_basic_training():
    """基本训练示例 - 使用默认配置"""
    print("🎯 示例1: 基本训练（默认配置）")
    print("-" * 40)
    
    from failure_prediction.model import train_failure_prediction_model
    
    # 直接使用默认配置训练
    # SwanLab会自动记录所有配置参数
    print("开始训练...")
    print("SwanLab将自动记录:")
    print("  - 模型架构: LSTM")
    print("  - 数据平衡: 过采样")
    print("  - 损失函数: Focal Loss")
    print("  - 时间去重: 关闭（数据已预处理）")
    print("  - 所有超参数和配置")
    
    # 注意：这里只是示例，实际训练需要数据
    # model, trainer = train_failure_prediction_model()
    
    print("✅ 基本训练配置完成")

def example_custom_config():
    """自定义配置示例"""
    print("\n🎯 示例2: 自定义配置")
    print("-" * 40)
    
    from failure_prediction.model import (
        FailurePredictionConfig,
        FailurePredictionModel
    )
    
    # 创建自定义配置
    config = FailurePredictionConfig()
    
    # 模型配置
    config.model_type = "transformer"
    config.hidden_dim = 512
    config.num_layers = 6
    config.num_heads = 8
    config.dropout_rate = 0.2
    
    # 数据配置
    config.enable_time_deduplication = True  # 启用时间去重
    config.use_oversampling = True
    config.oversampling_ratio = 0.25
    
    # 损失函数配置
    config.use_focal_loss = False
    config.use_label_smoothing = True
    config.label_smoothing = 0.1
    
    # 训练配置
    config.learning_rate = 0.0005
    config.batch_size = 64
    config.max_epochs = 150
    config.use_lr_scheduler = True
    config.lr_scheduler_type = "cosine"
    config.lr_warmup_epochs = 10
    
    print("✅ 自定义配置创建完成")
    print(f"   - 模型: {config.model_type.upper()}")
    print(f"   - 隐藏维度: {config.hidden_dim}")
    print(f"   - 时间去重: {config.enable_time_deduplication}")
    print(f"   - 损失函数: 标签平滑")
    print(f"   - 学习率调度: {config.lr_scheduler_type}")
    
    # 显示SwanLab配置预览
    swanlab_config = {
        **config.get_model_info(),
        **config.get_training_info(), 
        **config.get_data_info(),
        **config.get_loss_info(),
    }
    
    print(f"\n📊 SwanLab将记录 {len(swanlab_config)} 个配置参数:")
    for category, method in [
        ("模型", config.get_model_info),
        ("训练", config.get_training_info),
        ("数据", config.get_data_info),
        ("损失", config.get_loss_info)
    ]:
        info = method()
        print(f"   - {category}: {len(info)} 项")

def example_config_comparison():
    """配置对比示例"""
    print("\n🎯 示例3: 配置对比")
    print("-" * 40)
    
    from failure_prediction.model import FailurePredictionConfig
    
    # 创建不同的配置进行对比
    configs = {
        "LSTM-基础": {
            "model_type": "lstm",
            "hidden_dim": 128,
            "use_focal_loss": True,
            "enable_time_deduplication": False
        },
        "GRU-增强": {
            "model_type": "gru", 
            "hidden_dim": 256,
            "use_label_smoothing": True,
            "enable_time_deduplication": True
        },
        "Transformer-高级": {
            "model_type": "transformer",
            "hidden_dim": 512,
            "num_heads": 16,
            "use_focal_loss": True,
            "enable_time_deduplication": False
        }
    }
    
    print("📋 配置对比表:")
    print(f"{'配置名称':<15} {'模型':<12} {'隐藏维度':<8} {'损失函数':<12} {'时间去重':<8}")
    print("-" * 65)
    
    for name, config_dict in configs.items():
        config = FailurePredictionConfig()
        config.update_from_dict(config_dict)
        
        loss_info = config.get_loss_info()
        loss_type = loss_info["loss_type"]
        
        print(f"{name:<15} {config.model_type.upper():<12} {config.hidden_dim:<8} {loss_type:<12} {config.enable_time_deduplication!s:<8}")
    
    print("\n💡 每个配置都会在SwanLab中创建独立的实验")
    print("   实验名称格式: {model_type}-{hidden_dim}d-{num_layers}l")

def example_swanlab_features():
    """SwanLab功能展示"""
    print("\n🎯 示例4: SwanLab功能展示")
    print("-" * 40)
    
    from failure_prediction.model import FailurePredictionConfig
    
    config = FailurePredictionConfig()
    config.model_type = "lstm"
    config.hidden_dim = 256
    
    print("📈 SwanLab将自动记录以下信息:")
    
    print("\n🏗️  模型信息:")
    model_info = config.get_model_info()
    for key, value in model_info.items():
        print(f"   - {key}: {value}")
    
    print("\n🎓 训练信息:")
    training_info = config.get_training_info()
    for key, value in training_info.items():
        print(f"   - {key}: {value}")
    
    print("\n📊 数据信息:")
    data_info = config.get_data_info()
    for key, value in data_info.items():
        print(f"   - {key}: {value}")
    
    print("\n📉 损失信息:")
    loss_info = config.get_loss_info()
    for key, value in loss_info.items():
        if value is not None:
            print(f"   - {key}: {value}")
    
    print("\n🔄 训练过程中还会记录:")
    print("   - 训练/验证损失")
    print("   - 准确率、精确率、召回率、F1分数")
    print("   - AUC、混淆矩阵")
    print("   - 学习率变化")
    print("   - 模型参数数量和大小")

def example_time_deduplication():
    """时间去重配置示例"""
    print("\n🎯 示例5: 时间去重配置")
    print("-" * 40)
    
    from failure_prediction.model import FailurePredictionConfig
    
    print("⏰ 时间去重配置说明:")
    print("   - enable_time_deduplication = False (默认)")
    print("     适用于: 数据已经预处理去重的情况")
    print("     效果: 直接使用过滤后的特征，提高处理效率")
    
    print("\n   - enable_time_deduplication = True")
    print("     适用于: 原始数据可能有重复时间戳的情况")
    print("     效果: 在处理过程中去除相邻重复时间戳")
    
    # 示例配置
    config_no_dedup = FailurePredictionConfig()
    config_no_dedup.enable_time_deduplication = False
    
    config_with_dedup = FailurePredictionConfig()
    config_with_dedup.enable_time_deduplication = True
    
    print(f"\n📊 配置对比:")
    print(f"   - 无去重配置: {config_no_dedup.get_data_info()['time_deduplication']}")
    print(f"   - 启用去重配置: {config_with_dedup.get_data_info()['time_deduplication']}")
    
    print("\n💡 建议:")
    print("   - 当前数据已预处理，建议使用默认设置 (False)")
    print("   - 如果处理新的原始数据，可考虑启用 (True)")

def main():
    """主函数"""
    print("🎯 SwanLab集成训练示例")
    print("=" * 60)
    
    examples = [
        example_basic_training,
        example_custom_config,
        example_config_comparison,
        example_swanlab_features,
        example_time_deduplication,
    ]
    
    for example in examples:
        example()
    
    print("\n" + "=" * 60)
    print("🎉 所有示例展示完成！")
    print("\n🚀 开始实际训练:")
    print("   from failure_prediction.model import train_failure_prediction_model")
    print("   model, trainer = train_failure_prediction_model()")
    print("\n📊 查看SwanLab实验:")
    print("   访问 SwanLab Web界面查看实验结果")
    print("   所有配置参数都会自动记录和对比")

if __name__ == "__main__":
    main()
