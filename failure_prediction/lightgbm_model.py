"""
LightGBM 故障预测模型
基于序列处理的故障预测，使用 LightGBM 进行分类
"""

import os
import gzip
import json
import numpy as np
from collections import defaultdict
import random
from datetime import datetime, timedelta
import pickle

# 数据处理和评估
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    confusion_matrix,
)
from imblearn.over_sampling import SMOTE, RandomOverSampler

# LightGBM
try:
    import lightgbm as lgb

    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠️  LightGBM不可用，将无法使用LightGBM模型")

# SwanLab 实验跟踪
try:
    import swanlab

    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False
    print("⚠️  SwanLab不可用，将使用本地日志")

from rich.console import Console

console = Console()


class FailurePredictionConfig:
    """故障预测配置类 - 统一使用序列处理模式"""

    def __init__(self):
        """初始化配置参数"""
        # 使用os包管理相对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)  # 获取项目根目录

        # 构建相对路径
        self.data_path = os.path.join(
            project_root,
            "failure_prediction",
            "processed",
            "processed_new_data_20250927_214625.json.gz",
        )
        self.output_dir = os.path.join(project_root, "failure_prediction", "models")
        self.logs_dir = os.path.join(project_root, "failure_prediction", "logs")

        # 确保路径存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)

        # 验证数据文件是否存在
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")

        self.random_seed = 42

        # 数据参数
        self.val_size = 0.15
        self.train_size = 0.7
        self.test_size = 1 - self.train_size - self.val_size

        # 序列处理参数
        self.max_seq_length = 100  # 最大序列长度
        self.min_seq_length = 2  # 最小序列长度
        self.feature_dim = 16  # 单个时间步的特征维度（不含时间戳）

        # 时间过滤参数
        self.filter_tp_before_hours = 1.0  # 过滤TP前1小时内的记录（设置为None禁用）
        self.use_prediction_mode = True  # 是否使用预测模式（过滤TP时刻后的记录）

        # 时间去重参数
        self.enable_time_deduplication = False  # 是否启用相邻时间去重

        # LightGBM 模型参数
        self.lgb_params = {
            "objective": "binary",
            "metric": "binary_logloss",
            "boosting_type": "gbdt",
            "num_leaves": 31,
            "learning_rate": 0.05,
            "feature_fraction": 0.9,
            "bagging_fraction": 0.8,
            "bagging_freq": 5,
            "verbose": 0,
            "random_state": self.random_seed,
        }

        # 数据平衡策略
        self.use_oversampling = True  # 使用过采样而不是权重
        self.oversampling_method = "random"  # "random", "smote"
        self.oversampling_ratio = 0.3  # 过采样后正样本比例（降低一些避免过拟合）

        # 训练参数
        self.early_stopping_rounds = 50  # 减少早停轮数
        self.num_boost_round = 200  # 减少训练轮数


class LightGBMFailurePredictionDataset:
    """LightGBM 故障预测数据集 - 基于序列处理"""

    def __init__(self, data, config, feature_scaler=None):
        self.data = data
        self.config = config
        self.feature_scaler = feature_scaler

        # 使用序列模式的预处理逻辑
        self._preprocess_sequence_data()

    def _parse_timestamp(self, timestamp_str, formats=None):
        """解析时间戳（优化版本）"""
        if formats is None:
            formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]

        try:
            # 尝试不同的时间格式
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            # 如果都失败，返回None
            return None
        except:
            return None

    def _preprocess_sequence_data(self):
        """序列模式数据预处理（优化版本）"""
        sequences = []
        labels = []
        time_diffs_list = []
        lengths = []

        # 预编译时间格式以提升解析效率
        time_formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y/%m/%d %H:%M:%S"]

        # 批量处理进度显示
        total_items = len(self.data)
        processed_count = 0
        valid_count = 0

        console.print(f"[cyan]开始处理 {total_items:,} 个数据样本...[/cyan]")

        for item_idx, item in enumerate(self.data):
            # 进度显示（每处理10000个样本显示一次）
            if item_idx % 10000 == 0 and item_idx > 0:
                console.print(
                    f"[cyan]已处理 {item_idx:,}/{total_items:,} 样本，有效样本: {len(sequences):,}[/cyan]"
                )

            # 获取特征列表
            feature_list = item["feature_list"]
            if not feature_list or len(feature_list) < self.config.min_seq_length:
                continue

            try:
                # 优化：预先获取TP时间戳，避免后续无效处理
                tp_timestamp_str = item["group_key"][3]
                tp_timestamp = self._parse_timestamp(
                    str(tp_timestamp_str), time_formats
                )

                if tp_timestamp is None:
                    # 只抽样输出TP时间戳解析错误（每1000个样本输出一次）
                    if item_idx % 1000 == 0:
                        console.print(
                            f"[yellow]⚠️  无法解析TP时间戳: {tp_timestamp_str}, 跳过此样本[/yellow]"
                        )
                    continue

                # 解析时间戳并排序（优化：使用列表推导式）
                timestamped_features = []
                for i, feature in enumerate(feature_list):
                    if len(feature) > 0:
                        timestamp_str = feature[0]  # 时间戳在第一个位置
                        timestamp = self._parse_timestamp(
                            str(timestamp_str), time_formats
                        )
                        if timestamp:
                            timestamped_features.append((timestamp, feature, i))

                if len(timestamped_features) < self.config.min_seq_length:
                    continue

                # 按时间排序
                timestamped_features.sort(key=lambda x: x[0])

                # 过滤记录
                filtered_features = []
                if (
                    self.config.use_prediction_mode
                    and self.config.filter_tp_before_hours is not None
                ):
                    # 计算过滤时间窗口
                    filter_before_time = tp_timestamp - timedelta(
                        hours=self.config.filter_tp_before_hours
                    )

                    # 只保留在过滤时间之前的记录
                    for timestamp, feature, idx in timestamped_features:
                        if timestamp < filter_before_time:
                            filtered_features.append((timestamp, feature, idx))

                    # 只在异常情况下且抽样输出（过滤掉太多记录）
                    if (
                        len(filtered_features) < len(timestamped_features) * 0.5
                        and len(sequences) % 50 == 0
                    ):
                        console.print(
                            f"[yellow]⚠️  过滤掉大量记录: 原始{len(timestamped_features)} -> 过滤后{len(filtered_features)}[/yellow]"
                        )
                else:
                    filtered_features = timestamped_features

                if len(filtered_features) < self.config.min_seq_length:
                    # 只抽样输出严重异常情况（每100个样本输出一次）
                    if (
                        len(filtered_features) < self.config.min_seq_length // 2
                        and len(sequences) % 100 == 0
                    ):
                        console.print(
                            f"[yellow]⚠️  过滤后记录数严重不足: {len(filtered_features)}, 跳过此样本[/yellow]"
                        )
                    continue

                # 提取特征和计算相对时间
                base_time = filtered_features[0][0]
                seq_features = []
                time_diffs = []

                # 直接使用过滤后的特征，不进行去重
                processed_features = filtered_features[
                    : self.config.max_seq_length
                ]  # 限制最大长度

                # 优化：批量处理特征
                for timestamp, feature, _ in processed_features:
                    try:
                        # 计算相对时间差（分钟）
                        time_diff = (timestamp - base_time).total_seconds() / 60.0
                        time_diffs.append(time_diff)

                        # 优化：使用numpy进行批量特征处理
                        feature_vals = feature[1:]  # 跳过时间戳

                        # 转换为float数组，无效值设为0
                        processed_vals = []
                        for val in feature_vals:
                            try:
                                if val is None:
                                    processed_vals.append(0.0)
                                else:
                                    float_val = float(val)
                                    processed_vals.append(
                                        float_val if np.isfinite(float_val) else 0.0
                                    )
                            except (ValueError, TypeError):
                                processed_vals.append(0.0)

                        # 补齐或截断到指定维度
                        if len(processed_vals) < self.config.feature_dim:
                            processed_vals.extend(
                                [0.0] * (self.config.feature_dim - len(processed_vals))
                            )
                        else:
                            processed_vals = processed_vals[: self.config.feature_dim]

                        seq_features.append(processed_vals)

                    except Exception as e:
                        # 只在特定情况下输出异常（减少输出量）
                        if item_idx % 1000 == 0 and (
                            "invalid literal" in str(e) or "cannot convert" in str(e)
                        ):
                            console.print(f"[yellow]⚠️  特征解析错误: {e}[/yellow]")
                        continue

                if len(seq_features) < self.config.min_seq_length:
                    continue

                # 转换为numpy数组
                seq_features = np.array(seq_features, dtype=np.float32)
                time_diffs = np.array(time_diffs, dtype=np.float32)

                # 特征标准化
                if self.feature_scaler is None:
                    self.feature_scaler = StandardScaler()
                    seq_features = self.feature_scaler.fit_transform(seq_features)
                else:
                    seq_features = self.feature_scaler.transform(seq_features)

                sequences.append(seq_features)
                time_diffs_list.append(time_diffs)
                lengths.append(len(seq_features))

                # 处理标签
                original_label = item["label_list"][0]
                new_label = 1 if original_label <= 4 else 0
                labels.append(new_label)

            except Exception as e:
                # 只在严重错误时输出（减少输出量）
                if "KeyError" in str(e) or "IndexError" in str(e):
                    console.print(f"[yellow]⚠️  序列处理严重错误: {e}[/yellow]")
                continue

        self.sequences = sequences
        self.features = None  # 序列模式下不使用单特征
        self.labels = np.array(labels, dtype=np.int32)
        self.time_diffs = time_diffs_list
        self.lengths = np.array(lengths, dtype=np.int32)

        console.print(
            f"✅ 处理完成！有效序列: {len(sequences):,}/{total_items:,} ({len(sequences)/total_items*100:.1f}%)"
        )

        # 应用过采样（仅对训练集）
        if (
            self.config.use_oversampling and self.feature_scaler is None
        ):  # 只对训练集过采样
            self._apply_oversampling()

    def _apply_oversampling(self):
        """应用过采样平衡数据集"""
        console.print("[cyan]🔄 应用过采样平衡数据集...[/cyan]")

        # 统计原始类别分布
        unique, counts = np.unique(self.labels, return_counts=True)
        original_dist = dict(zip(unique, counts))
        console.print(f"原始类别分布: {original_dist}")

        # 计算需要的正样本数量
        neg_count = original_dist.get(0, 0)
        pos_count = original_dist.get(1, 0)

        if pos_count == 0:
            console.print("[yellow]⚠️  没有正样本，跳过过采样[/yellow]")
            return

        # 计算目标正样本数量
        target_pos_count = int(
            neg_count
            * self.config.oversampling_ratio
            / (1 - self.config.oversampling_ratio)
        )

        if target_pos_count <= pos_count:
            console.print(
                f"[yellow]⚠️  正样本数量已足够 ({pos_count} >= {target_pos_count})，跳过过采样[/yellow]"
            )
            return

        # 找到所有正样本的索引
        pos_indices = np.where(self.labels == 1)[0]

        # 随机过采样正样本
        if self.config.oversampling_method == "random":
            # 计算需要复制的数量
            samples_to_add = target_pos_count - pos_count

            # 随机选择正样本进行复制
            np.random.seed(self.config.random_seed)
            indices_to_duplicate = np.random.choice(
                pos_indices, size=samples_to_add, replace=True
            )

            # 复制序列、时间差、长度和标签
            for idx in indices_to_duplicate:
                self.sequences.append(self.sequences[idx].copy())
                self.time_diffs.append(self.time_diffs[idx].copy())
                self.lengths = np.append(self.lengths, self.lengths[idx])

            # 添加对应的标签
            new_labels = np.ones(samples_to_add, dtype=np.int32)
            self.labels = np.concatenate([self.labels, new_labels])

        elif self.config.oversampling_method == "smote":
            # 使用SMOTE进行过采样（需要将序列数据展平）
            console.print(
                "[yellow]⚠️  SMOTE过采样对序列数据较复杂，使用随机过采样[/yellow]"
            )
            # 暂时使用随机过采样的逻辑
            samples_to_add = target_pos_count - pos_count
            np.random.seed(self.config.random_seed)
            indices_to_duplicate = np.random.choice(
                pos_indices, size=samples_to_add, replace=True
            )

            for idx in indices_to_duplicate:
                self.sequences.append(self.sequences[idx].copy())
                self.time_diffs.append(self.time_diffs[idx].copy())
                self.lengths = np.append(self.lengths, self.lengths[idx])

            new_labels = np.ones(samples_to_add, dtype=np.int32)
            self.labels = np.concatenate([self.labels, new_labels])

        # 统计过采样后的类别分布
        unique, counts = np.unique(self.labels, return_counts=True)
        new_dist = dict(zip(unique, counts))
        console.print(f"过采样后类别分布: {new_dist}")
        console.print(f"✅ 过采样完成！数据集大小: {len(self.sequences):,}")

    def _extract_sequence_features(self, sequence, time_diffs, length):
        """从序列数据中提取特征用于LightGBM"""
        # 将序列转换为固定长度的特征向量

        # 1. 基础统计特征
        seq_mean = np.mean(sequence, axis=0)  # 每个特征的均值
        seq_std = np.std(sequence, axis=0)  # 每个特征的标准差
        seq_min = np.min(sequence, axis=0)  # 每个特征的最小值
        seq_max = np.max(sequence, axis=0)  # 每个特征的最大值

        # 2. 时间特征
        time_mean = np.mean(time_diffs) if len(time_diffs) > 0 else 0
        time_std = np.std(time_diffs) if len(time_diffs) > 0 else 0
        time_range = (
            np.max(time_diffs) - np.min(time_diffs) if len(time_diffs) > 0 else 0
        )

        # 3. 序列长度特征
        seq_length = length

        # 4. 趋势特征（前后半段的差异）
        mid_point = length // 2
        if mid_point > 0:
            first_half_mean = np.mean(sequence[:mid_point], axis=0)
            second_half_mean = np.mean(sequence[mid_point:], axis=0)
            trend_features = second_half_mean - first_half_mean
        else:
            trend_features = np.zeros_like(seq_mean)

        # 5. 变化率特征
        if length > 1:
            # 计算相邻时间步的差值
            diff_features = np.mean(np.abs(np.diff(sequence, axis=0)), axis=0)
        else:
            diff_features = np.zeros_like(seq_mean)

        # 6. 组合特征
        combined_features = np.concatenate(
            [
                seq_mean,  # 16个特征
                seq_std,  # 16个特征
                seq_min,  # 16个特征
                seq_max,  # 16个特征
                [time_mean, time_std, time_range, seq_length],  # 4个时间和长度特征
                trend_features,  # 16个趋势特征
                diff_features,  # 16个变化率特征
            ]
        )

        return combined_features

    def get_feature_matrix(self):
        """获取用于LightGBM的特征矩阵"""
        console.print("[cyan]🔄 正在将序列数据转换为特征矩阵...[/cyan]")

        feature_list = []
        labels = []

        for i, (sequence, time_diffs, length) in enumerate(
            zip(self.sequences, self.time_diffs, self.lengths)
        ):
            # 提取特征
            features = self._extract_sequence_features(sequence, time_diffs, length)
            feature_list.append(features)
            labels.append(self.labels[i])

            # 进度显示
            if (i + 1) % 10000 == 0:
                console.print(f"[cyan]已处理 {i+1:,} 个序列...[/cyan]")

        feature_matrix = np.array(feature_list)
        labels = np.array(labels)

        console.print(f"✅ 特征转换完成！特征矩阵形状: {feature_matrix.shape}")

        return feature_matrix, labels


class LightGBMFailurePredictor:
    """LightGBM故障预测器"""

    def __init__(self, config):
        self.config = config
        self.model = None
        self.feature_scaler = None

        if not LIGHTGBM_AVAILABLE:
            raise ImportError("LightGBM不可用，请安装 lightgbm 包")

    def _extract_features_from_data(self, data):
        """从原始数据中提取特征"""
        # 创建临时数据集
        temp_dataset = LightGBMFailurePredictionDataset(data, self.config)

        # 获取特征矩阵和标签
        X, y = temp_dataset.get_feature_matrix()

        return X, y, temp_dataset.feature_scaler

    def train(self, train_data, val_data=None):
        """训练LightGBM模型"""
        console.print("[bold green]🚀 开始训练LightGBM模型...[/bold green]")

        # 提取训练集特征
        X_train, y_train, self.feature_scaler = self._extract_features_from_data(
            train_data
        )

        console.print(
            f"📊 训练集特征形状: {X_train.shape}, 标签分布: {np.bincount(y_train)}"
        )

        # 准备验证集
        if val_data:
            X_val, y_val, _ = self._extract_features_from_data(val_data)
            console.print(
                f"📊 验证集特征形状: {X_val.shape}, 标签分布: {np.bincount(y_val)}"
            )
        else:
            # 如果没有验证集，使用训练集的一部分作为验证
            X_train, X_val, y_train, y_val = train_test_split(
                X_train,
                y_train,
                test_size=0.2,
                random_state=self.config.random_seed,
                stratify=y_train,
            )

        # 创建LightGBM数据集
        train_data_lgb = lgb.Dataset(X_train, label=y_train)
        val_data_lgb = lgb.Dataset(X_val, label=y_val, reference=train_data_lgb)

        # 训练模型
        self.model = lgb.train(
            self.config.lgb_params,
            train_data_lgb,
            num_boost_round=self.config.num_boost_round,
            valid_sets=[train_data_lgb, val_data_lgb],
            valid_names=["train", "valid"],
            callbacks=[
                lgb.early_stopping(stopping_rounds=self.config.early_stopping_rounds),
                lgb.log_evaluation(50),
            ],
        )

        # 评估训练结果
        train_pred = self.model.predict(
            X_train, num_iteration=self.model.best_iteration
        )
        val_pred = self.model.predict(X_val, num_iteration=self.model.best_iteration)

        train_pred_binary = (train_pred > 0.5).astype(int)
        val_pred_binary = (val_pred > 0.5).astype(int)

        # 计算指标
        train_metrics = {
            "accuracy": accuracy_score(y_train, train_pred_binary),
            "precision": precision_score(y_train, train_pred_binary),
            "recall": recall_score(y_train, train_pred_binary),
            "f1": f1_score(y_train, train_pred_binary),
            "auc": roc_auc_score(y_train, train_pred),
        }

        val_metrics = {
            "accuracy": accuracy_score(y_val, val_pred_binary),
            "precision": precision_score(y_val, val_pred_binary),
            "recall": recall_score(y_val, val_pred_binary),
            "f1": f1_score(y_val, val_pred_binary),
            "auc": roc_auc_score(y_val, val_pred),
        }

        console.print("\n[bold blue]📊 训练结果:[/bold blue]")
        console.print(
            f"  训练集 - Acc: {train_metrics['accuracy']:.4f}, Prec: {train_metrics['precision']:.4f}, Recall: {train_metrics['recall']:.4f}, F1: {train_metrics['f1']:.4f}, AUC: {train_metrics['auc']:.4f}"
        )
        console.print(
            f"  验证集 - Acc: {val_metrics['accuracy']:.4f}, Prec: {val_metrics['precision']:.4f}, Recall: {val_metrics['recall']:.4f}, F1: {val_metrics['f1']:.4f}, AUC: {val_metrics['auc']:.4f}"
        )

        return train_metrics, val_metrics

    def predict(self, test_data):
        """预测测试集"""
        console.print("[bold blue]🔮 开始预测...[/bold blue]")

        # 提取测试集特征
        X_test, y_test, _ = self._extract_features_from_data(test_data)

        # 预测
        test_pred = self.model.predict(X_test, num_iteration=self.model.best_iteration)
        test_pred_binary = (test_pred > 0.5).astype(int)

        # 计算指标
        test_metrics = {
            "accuracy": accuracy_score(y_test, test_pred_binary),
            "precision": precision_score(y_test, test_pred_binary),
            "recall": recall_score(y_test, test_pred_binary),
            "f1": f1_score(y_test, test_pred_binary),
            "auc": roc_auc_score(y_test, test_pred),
            "confusion_matrix": confusion_matrix(y_test, test_pred_binary),
        }

        return test_pred, test_pred_binary, test_metrics

    def save_model(self, filepath):
        """保存模型"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        model_data = {
            "model": self.model,
            "config": self.config,
            "feature_scaler": self.feature_scaler,
        }

        with open(filepath, "wb") as f:
            pickle.dump(model_data, f)

        console.print(f"[green]💾 模型已保存到: {filepath}[/green]")

    def load_model(self, filepath):
        """加载模型"""
        with open(filepath, "rb") as f:
            model_data = pickle.load(f)

        self.model = model_data["model"]
        self.config = model_data["config"]
        self.feature_scaler = model_data["feature_scaler"]

        console.print(f"[green]📂 模型已加载从: {filepath}[/green]")


def load_and_split_data(config):
    """加载数据并按照TP分组划分"""
    console.print("[bold cyan]📖 加载数据...[/bold cyan]")

    with gzip.open(config.data_path, "rt", encoding="utf-8") as f:
        data = json.load(f)

    console.print(f"✅ 加载了 {len(data)} 个分组数据")

    # 按照TP分组
    tp_groups = defaultdict(list)
    for item in data:
        tp_value = item["group_key"][3]  # TP字段
        tp_groups[tp_value].append(item)

    # 划分TP组
    tp_list = list(tp_groups.keys())
    random.seed(config.random_seed)
    random.shuffle(tp_list)

    train_tp_count = int(len(tp_list) * config.train_size)
    val_tp_count = int(len(tp_list) * config.val_size)

    train_tp = tp_list[:train_tp_count]
    val_tp = tp_list[train_tp_count : train_tp_count + val_tp_count]
    test_tp = tp_list[train_tp_count + val_tp_count :]

    # 收集数据
    train_data = []
    val_data = []
    test_data = []

    for tp in train_tp:
        train_data.extend(tp_groups[tp])
    for tp in val_tp:
        val_data.extend(tp_groups[tp])
    for tp in test_tp:
        test_data.extend(tp_groups[tp])

    console.print("📊 数据划分完成:")
    console.print(f"  训练集: {len(train_data)} 样本")
    console.print(f"  验证集: {len(val_data)} 样本")
    console.print(f"  测试集: {len(test_data)} 样本")

    return train_data, val_data, test_data


def test_data_processing():
    """测试数据处理功能"""
    console.print(f"[bold green]🌳 测试数据处理功能[/bold green]")
    console.print("=" * 60)

    # 配置
    config = FailurePredictionConfig()

    # 加载少量数据进行测试
    with gzip.open(config.data_path, "rt", encoding="utf-8") as f:
        data = json.load(f)

    # 只取前1000个样本进行快速测试
    test_data = data[:1000]
    console.print(f"✅ 加载了 {len(test_data)} 个测试样本")

    # 创建数据集
    dataset = LightGBMFailurePredictionDataset(test_data, config)

    console.print(f"📊 处理完成！有效序列: {len(dataset.sequences)}")

    # 检查是否有重复的时间戳
    total_records = 0
    duplicate_count = 0

    for i, (sequence, time_diffs, length) in enumerate(
        zip(dataset.sequences, dataset.time_diffs, dataset.lengths)
    ):
        total_records += length
        # 检查时间戳是否有重复
        unique_times = len(set(time_diffs))
        if unique_times < length:
            duplicate_count += 1

        if i < 5:  # 只显示前5个样本的详细信息
            console.print(
                f"样本 {i}: 长度={length}, 唯一时间戳={unique_times}, 时间戳={time_diffs[:5]}..."
            )

    console.print(f"\n📈 统计信息:")
    console.print(f"  总记录数: {total_records}")
    console.print(f"  有重复时间戳的序列: {duplicate_count}/{len(dataset.sequences)}")
    console.print(f"  平均序列长度: {np.mean(dataset.lengths):.2f}")
    console.print(f"  标签分布: {np.bincount(dataset.labels)}")

    return dataset


def quick_train_lightgbm():
    """快速训练LightGBM模型（使用少量数据）"""
    console.print(f"[bold green]🌳 LightGBM快速训练测试[/bold green]")
    console.print("=" * 60)

    # 配置
    config = FailurePredictionConfig()

    # 减少训练数据量
    config.num_boost_round = 100  # 进一步减少训练轮数
    config.early_stopping_rounds = 20

    # 设置随机种子
    random.seed(config.random_seed)
    np.random.seed(config.random_seed)

    # 加载少量数据
    with gzip.open(config.data_path, "rt", encoding="utf-8") as f:
        all_data = json.load(f)

    # 随机选择较少的数据进行快速测试
    sample_size = 10000  # 只使用1万样本
    indices = random.sample(range(len(all_data)), min(sample_size, len(all_data)))
    sample_data = [all_data[i] for i in indices]

    # 简单的数据划分（不按TP分组）
    train_size = int(0.7 * len(sample_data))
    val_size = int(0.15 * len(sample_data))

    train_data = sample_data[:train_size]
    val_data = sample_data[train_size:train_size + val_size]
    test_data = sample_data[train_size + val_size:]

    console.print(f"📊 使用样本数据: 训练={len(train_data)}, 验证={len(val_data)}, 测试={len(test_data)}")

    # 创建模型
    model = LightGBMFailurePredictor(config)

    # 训练模型
    train_metrics, val_metrics = model.train(train_data, val_data)

    # 测试模型
    console.print("[bold blue]🧪 开始测试...[/bold blue]")
    test_pred, test_pred_binary, test_metrics = model.predict(test_data)

    # 打印测试结果
    console.print("\n[bold green]📊 测试结果:[/bold green]")
    console.print(f"  准确率 (Accuracy): {test_metrics['accuracy']:.4f}")
    console.print(f"  精确率 (Precision): {test_metrics['precision']:.4f}")
    console.print(f"  召回率 (Recall): {test_metrics['recall']:.4f}")
    console.print(f"  F1分数 (F1-Score): {test_metrics['f1']:.4f}")
    console.print(f"  AUC得分: {test_metrics['auc']:.4f}")

    console.print("\n[bold blue]混淆矩阵 (Confusion Matrix):[/bold blue]")
    console.print(f"{test_metrics['confusion_matrix']}")

    console.print("[bold blue]🎉 LightGBM快速训练测试完成！[/bold blue]")

    return model, test_metrics


def train_lightgbm_failure_prediction():
    """训练LightGBM故障预测模型的主函数"""
    console.print(f"[bold green]🌳 LightGBM故障预测模型训练[/bold green]")
    console.print("=" * 60)

    # 配置
    config = FailurePredictionConfig()

    # 设置随机种子
    random.seed(config.random_seed)
    np.random.seed(config.random_seed)

    # 加载和划分数据
    train_data, val_data, test_data = load_and_split_data(config)

    # 创建模型
    model = LightGBMFailurePredictor(config)

    # 训练模型
    train_metrics, val_metrics = model.train(train_data, val_data)

    # 测试模型
    console.print("[bold blue]🧪 开始测试...[/bold blue]")
    test_pred, test_pred_binary, test_metrics = model.predict(test_data)

    # 打印测试结果
    console.print("\n[bold green]📊 测试结果:[/bold green]")
    console.print(f"  准确率 (Accuracy): {test_metrics['accuracy']:.4f}")
    console.print(f"  精确率 (Precision): {test_metrics['precision']:.4f}")
    console.print(f"  召回率 (Recall): {test_metrics['recall']:.4f}")
    console.print(f"  F1分数 (F1-Score): {test_metrics['f1']:.4f}")
    console.print(f"  AUC得分: {test_metrics['auc']:.4f}")

    console.print("\n[bold blue]混淆矩阵 (Confusion Matrix):[/bold blue]")
    console.print(f"{test_metrics['confusion_matrix']}")

    # 保存模型
    model_path = os.path.join(
        config.output_dir,
        f"lightgbm_failure_prediction_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl",
    )

    model.save_model(model_path)
    console.print("[bold blue]🎉 LightGBM模型训练完成！[/bold blue]")

    return model, test_metrics


if __name__ == "__main__":
    import sys

    # 检查LightGBM是否可用
    if not LIGHTGBM_AVAILABLE:
        console.print("[red]❌ LightGBM不可用，请安装 lightgbm 包[/red]")
        exit(1)

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            # 运行数据处理测试
            console.print("[bold yellow]🧪 运行数据处理测试...[/bold yellow]")
            dataset = test_data_processing()
        elif sys.argv[1] == "quick":
            # 运行快速训练测试
            console.print("[bold yellow]⚡ 运行快速训练测试...[/bold yellow]")
            model, metrics = quick_train_lightgbm()
        else:
            # 运行完整训练
            model, metrics = train_lightgbm_failure_prediction()
    else:
        # 默认运行完整训练
        model, metrics = train_lightgbm_failure_prediction()
