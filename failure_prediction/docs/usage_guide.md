# 故障预测模型使用指南

## 🎯 快速开始

### 基本使用
```python
from failure_prediction.model import train_failure_prediction_model

# 直接开始训练（使用默认配置）
model, trainer = train_failure_prediction_model()
```

### 自定义配置
```python
from failure_prediction.model import (
    FailurePredictionConfig,
    FailurePredictionModel,
    load_and_split_data,
    create_data_loaders
)
import pytorch_lightning as pl

# 创建自定义配置
config = FailurePredictionConfig()
config.batch_size = 64  # 调整批大小
config.learning_rate = 0.001  # 调整学习率
config.max_epochs = 50  # 调整训练轮数

# 加载数据
train_data, val_data, test_data = load_and_split_data(config)

# 创建数据加载器
train_loader, val_loader, test_loader, feature_scaler = create_data_loaders(
    train_data, val_data, test_data, config
)

# 创建模型
model = FailurePredictionModel(config)

# 创建训练器
trainer = pl.Trainer(
    accelerator=config.accelerator,
    devices=config.devices,
    max_epochs=config.max_epochs,
    enable_progress_bar=True,
)

# 开始训练
trainer.fit(model, train_loader, val_loader)
```

## ⚙️ 配置参数说明

### 数据参数
```python
config.val_size = 0.15          # 验证集比例
config.train_size = 0.7         # 训练集比例
config.max_seq_length = 100     # 最大序列长度
config.min_seq_length = 2       # 最小序列长度
config.feature_dim = 16         # 特征维度
```

### 时间过滤参数
```python
config.filter_tp_before_hours = 1.0    # TP前过滤时间（小时）
config.use_prediction_mode = True      # 是否使用预测模式
```

### 模型参数
```python
config.hidden_dim = 128         # 隐藏层维度
config.dropout_rate = 0.3       # Dropout比例
```

### 训练参数
```python
config.batch_size = 32          # 批大小
config.learning_rate = 0.01     # 学习率
config.max_epochs = 100         # 最大训练轮数
config.patience = 10            # 早停耐心值
config.weight_decay = 1e-4      # 权重衰减
```

### 类别不平衡处理
```python
config.use_class_weights = True     # 是否使用类别权重
config.pos_weight_scale = 13.5      # 正样本权重倍数
```

## 📊 性能优化

### 数据预处理优化
- ✅ **处理速度**: ~2,800 样本/秒
- ✅ **50万样本预计时间**: ~3-4分钟
- ✅ **有效样本率**: >99%

### 训练优化
```python
# 使用多进程数据加载
num_workers = 4

# 启用GPU内存固定（如果有GPU）
pin_memory = True

# 使用持久化工作进程
persistent_workers = True
```

## 🔧 故障排除

### 常见问题

#### 1. 张量维度不匹配
**问题**: `Target size must be the same as input size`
**解决**: 已修复，确保标签维度正确

#### 2. 数据预处理慢
**问题**: 50万样本处理时间过长
**解决**: 已优化，处理时间从>10分钟降至~3分钟

#### 3. 内存不足
**解决方案**:
```python
# 减少批大小
config.batch_size = 16

# 减少工作进程数
num_workers = 2

# 禁用内存固定
pin_memory = False
```

#### 4. GPU相关警告
**警告**: `Can't initialize NVML`
**说明**: 这是正常的警告，不影响训练

## 📈 监控训练

### 关键指标
- **训练损失** (train_loss): 应该逐渐下降
- **验证F1分数** (val_f1): 主要优化目标
- **验证AUC** (val_auc): 模型判别能力
- **验证召回率** (val_recall): 故障检测能力

### 早停机制
- 监控指标: `val_f1`
- 耐心值: 10个epoch
- 模式: 最大化

### 模型保存
- 自动保存最佳模型（基于val_f1）
- 保存路径: `failure_prediction/models/`
- 文件格式: `.pth`

## 🧪 测试和验证

### 运行测试
```bash
# 基本功能测试
python3 failure_prediction/test_optimized_model.py

# 完整训练流程测试
python3 -c "from failure_prediction.model import train_failure_prediction_model; train_failure_prediction_model()"
```

### 预期结果
```
🎉 完整训练流程测试通过！
📝 优化总结:
   ✅ 张量维度不匹配问题已修复
   ✅ 数据预处理效率已优化
   ✅ 训练和验证步骤正常工作
   ✅ 模型可以正常开始训练
```

## 📝 最佳实践

### 1. 数据准备
- 确保数据文件存在且格式正确
- 检查数据质量和完整性
- 注意时间戳格式的一致性

### 2. 训练配置
- 根据数据集大小调整批大小
- 监控训练过程中的指标变化
- 使用早停避免过拟合

### 3. 性能优化
- 使用GPU加速（如果可用）
- 启用多进程数据加载
- 合理设置工作进程数

### 4. 结果分析
- 关注混淆矩阵的详细结果
- 分析假阳性和假阴性的分布
- 根据业务需求调整阈值

## 🚀 进阶使用

### 自定义损失函数
```python
# 调整正样本权重
config.pos_weight_scale = 15.0  # 增加正样本权重

# 或者禁用类别权重
config.use_class_weights = False
```

### 模型集成
```python
# 训练多个模型进行集成
models = []
for seed in [42, 123, 456]:
    config.random_seed = seed
    model, trainer = train_failure_prediction_model()
    models.append(model)
```

### 超参数调优
```python
# 使用不同的学习率
for lr in [0.001, 0.01, 0.1]:
    config.learning_rate = lr
    # 训练并比较结果
```

现在您可以安全地使用优化后的故障预测模型进行训练了！
