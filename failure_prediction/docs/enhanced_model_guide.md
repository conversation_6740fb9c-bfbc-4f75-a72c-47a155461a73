# 增强故障预测模型指南

## 🚀 主要改进

### 1. 多种序列架构支持
- **MLP**: 简单的多层感知机（平均池化 + 分类器）
- **LSTM**: 长短期记忆网络（双向支持）
- **GRU**: 门控循环单元（双向支持）
- **Transformer**: 自注意力机制（位置编码 + 多头注意力）

### 2. 数据平衡策略
- **过采样**: 替代权重调整，直接平衡数据集
- **随机过采样**: 复制少数类样本
- **SMOTE**: 合成少数类过采样技术（序列版本）

### 3. 高级损失函数
- **Focal Loss**: 专门处理类别不平衡问题
- **标签平滑**: 防止过拟合，提高泛化能力
- **加权BCE**: 传统的类别权重方法

### 4. 优化器和调度器
- **AdamW**: 改进的Adam优化器
- **学习率调度**: Cosine/Step/Plateau调度
- **Warmup**: 学习率预热机制

## 📋 配置选项

### 模型架构配置
```python
config = FailurePredictionConfig()

# 选择模型类型
config.model_type = "lstm"  # "mlp", "lstm", "gru", "transformer"

# 模型参数
config.hidden_dim = 256      # 隐藏维度
config.num_layers = 3        # RNN/Transformer层数
config.dropout_rate = 0.4    # Dropout比例
config.bidirectional = True  # 双向RNN

# Transformer特定参数
config.num_heads = 8         # 注意力头数
config.dim_feedforward = 512 # 前馈网络维度
```

### 数据平衡配置
```python
# 过采样设置
config.use_oversampling = True
config.oversampling_method = "random"  # "random", "smote"
config.oversampling_ratio = 0.3        # 目标正样本比例

# 类别权重（当不使用过采样时）
config.use_class_weights = False
config.pos_weight_scale = 2
```

### 损失函数配置
```python
# Focal Loss（推荐用于不平衡数据）
config.use_focal_loss = True
config.focal_alpha = 0.25
config.focal_gamma = 2.0

# 标签平滑
config.use_label_smoothing = False
config.label_smoothing = 0.1
```

### 训练配置
```python
# 基础训练参数
config.batch_size = 32
config.learning_rate = 0.001  # 降低学习率
config.max_epochs = 100
config.patience = 15          # 增加耐心值

# 学习率调度
config.use_lr_scheduler = True
config.lr_scheduler_type = "cosine"  # "cosine", "step", "plateau"
config.lr_warmup_epochs = 5
```

## 🎯 推荐配置

### 配置1: LSTM + Focal Loss + 过采样
```python
config = FailurePredictionConfig()
config.model_type = "lstm"
config.hidden_dim = 256
config.num_layers = 3
config.bidirectional = True
config.dropout_rate = 0.4

# 数据平衡
config.use_oversampling = True
config.oversampling_ratio = 0.3

# 损失函数
config.use_focal_loss = True
config.focal_alpha = 0.25
config.focal_gamma = 2.0

# 训练
config.learning_rate = 0.001
config.use_lr_scheduler = True
config.lr_scheduler_type = "cosine"
```

### 配置2: Transformer + 标签平滑
```python
config = FailurePredictionConfig()
config.model_type = "transformer"
config.num_heads = 8
config.num_layers = 4
config.dim_feedforward = 512
config.dropout_rate = 0.3

# 数据平衡
config.use_oversampling = True
config.oversampling_ratio = 0.25

# 损失函数
config.use_label_smoothing = True
config.label_smoothing = 0.1

# 训练
config.learning_rate = 0.0005
config.lr_warmup_epochs = 10
```

## 📊 性能对比

### 模型复杂度
| 模型 | 参数量 | 训练速度 | 表征能力 |
|------|--------|----------|----------|
| MLP | ~46K | 最快 | 基础 |
| LSTM | ~3.9M | 中等 | 强 |
| GRU | ~2.9M | 中等 | 强 |
| Transformer | ~1.2M | 较慢 | 很强 |

### 损失函数特点
| 损失函数 | 适用场景 | 优势 | 注意事项 |
|----------|----------|------|----------|
| Focal Loss | 严重不平衡 | 专注困难样本 | 需调参 |
| 标签平滑 | 过拟合风险 | 提高泛化 | 可能降低精度 |
| 加权BCE | 轻度不平衡 | 简单有效 | 权重难调 |

## 🔧 使用示例

### 基本使用
```python
from failure_prediction.model import train_failure_prediction_model

# 使用默认增强配置
model, trainer = train_failure_prediction_model()
```

### 自定义训练
```python
from failure_prediction.model import (
    FailurePredictionConfig,
    FailurePredictionModel,
    load_and_split_data,
    create_data_loaders
)
import pytorch_lightning as pl

# 创建配置
config = FailurePredictionConfig()
config.model_type = "lstm"
config.use_focal_loss = True
config.use_oversampling = True

# 数据准备
train_data, val_data, test_data = load_and_split_data(config)
train_loader, val_loader, test_loader, _ = create_data_loaders(
    train_data, val_data, test_data, config
)

# 模型训练
model = FailurePredictionModel(config)
trainer = pl.Trainer(
    accelerator=config.accelerator,
    devices=config.devices,
    max_epochs=config.max_epochs,
    callbacks=[
        pl.callbacks.EarlyStopping(monitor="val_f1", patience=config.patience, mode="max"),
        pl.callbacks.ModelCheckpoint(monitor="val_f1", mode="max", save_top_k=3)
    ]
)

trainer.fit(model, train_loader, val_loader)
trainer.test(model, test_loader)
```

## 🎯 解决"全部预测为true"问题

### 问题原因
1. **类别严重不平衡**: 负样本远多于正样本
2. **模型表征能力不足**: 简单MLP无法捕获复杂模式
3. **损失函数不当**: 标准BCE对不平衡数据效果差

### 解决方案
1. **使用过采样**: 平衡训练数据分布
2. **采用Focal Loss**: 专注困难样本，减少简单负样本影响
3. **增强模型架构**: 使用LSTM/Transformer提升表征能力
4. **降低学习率**: 避免过快收敛到局部最优
5. **增加正则化**: 防止过拟合

### 监控指标
- **精确率**: 避免过多假阳性
- **召回率**: 确保捕获真正的故障
- **F1分数**: 平衡精确率和召回率
- **AUC**: 整体判别能力

## ⚠️ 注意事项

1. **内存使用**: Transformer和大型LSTM需要更多内存
2. **训练时间**: 复杂模型训练时间更长
3. **超参调优**: 不同架构需要不同的超参数
4. **数据质量**: 过采样可能放大噪声，注意数据清洗

## 🚀 下一步优化

1. **集成学习**: 组合多个模型的预测
2. **特征工程**: 添加更多时序特征
3. **注意力机制**: 在LSTM基础上添加注意力
4. **对抗训练**: 提高模型鲁棒性
5. **在线学习**: 支持增量更新

通过这些改进，模型应该能够显著提升在不平衡数据上的表现，避免"全部预测为true"的问题。
