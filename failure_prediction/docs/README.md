# Failure Prediction 故障预测项目文档

## 📋 文档概览

本文件夹包含了故障预测项目的完整文档说明，涵盖了项目的各个方面和实验过程。

## 📚 文档列表

### 🎯 核心文档

#### [序列模型总结](sequence_model_summary.md)
- **描述**: 详细介绍序列模型的设计思路、架构和实现
- **内容**:
  - 序列特征提取策略（相邻时间去重）
  - 时间序列建模方法
  - 模型架构设计
  - 训练和评估过程
  - 性能优化技巧

#### [实验总结报告](experiment_summary.md)
- **描述**: 记录所有实验过程、结果和分析
- **内容**:
  - 实验设计和设置
  - 数据预处理流程
  - 模型训练过程
  - 性能评估指标
  - 实验对比分析

#### [预测模式实验报告](prediction_mode_summary.md)
- **描述**: 专门针对预测模式进行深入分析
- **内容**:
  - TP前1小时过滤机制
  - 预测任务的特殊处理
  - 时序特征的重要性
  - 预测性能评估

## 🏗️ 项目结构

```
failure_prediction/
├── docs/                          # 📚 项目文档
│   ├── README.md                 # 📋 文档索引
│   ├── sequence_model_summary.md # 🔄 序列模型文档
│   ├── experiment_summary.md     # 📊 实验总结
│   └── prediction_mode_summary.md # 🎯 预测模式分析
├── src/                          # 💻 源代码
│   ├── lightning_model.py        # 统一模型实现（支持Lightning和序列模式）
│   ├── preprocess.py             # 数据预处理
│   ├── data_fetch.py             # 数据获取
│   └── download_data.py          # 数据下载
├── models/                       # 🤖 训练好的模型
├── logs/                         # 📈 训练日志
├── processed/                    # 📦 处理后的数据
└── __pycache__/                  # 🗂️ Python缓存
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 使用conda环境
conda activate algo

# 或使用pip安装依赖
pip install -r requirements.txt
```

### 2. 数据准备
```bash
python preprocess.py
```

### 3. 模型训练
```bash
# 训练Lightning版本
python lightning_model.py

# 或训练序列版本
# Lightning模式（默认）
python lightning_model.py

# 序列模式
python lightning_model.py sequence
```

## 📖 阅读指南

### 新手推荐阅读顺序：
1. **[实验总结报告](experiment_summary.md)** - 了解项目整体概况
2. **[序列模型总结](sequence_model_summary.md)** - 深入理解核心算法
3. **[预测模式实验报告](prediction_mode_summary.md)** - 了解预测任务的特殊处理

### 开发者推荐阅读顺序：
1. **[序列模型总结](sequence_model_summary.md)** - 核心算法和技术细节
2. **[实验总结报告](experiment_summary.md)** - 实验过程和结果分析
3. **[预测模式实验报告](prediction_mode_summary.md)** - 高级预测技术

## 🔧 核心特性

### 🎯 智能去重策略 
- **相邻时间去重**: 只去重连续重复的异常记录
- **保留间隔模式**: 保持异常序列的完整时序特征
- **提高预测准确性**: 避免因过度去重导致的信息丢失

### 📊 多模型支持
- **PyTorch Lightning版本**: 生产级代码，易于扩展
- **序列模型版本**: 专门针对时序数据优化
- **传统机器学习版本**: 快速原型验证

### 🔍 全面评估体系
- **多维度指标**: 准确率、精确率、召回率、F1分数、AUC
- **混淆矩阵分析**: 详细的分类性能分解
- **时序特征重要性**: 分析哪些特征对预测最重要

## 📈 实验结果

### 主要性能指标：
- **准确率**: 89.49%
- **精确率**: 89.49%
- **召回率**: 100.00%
- **F1分数**: 94.45%
- **AUC得分**: 79.16%

### 关键创新点：
- ✅ **时序特征提取**: 充分利用历史异常序列
- ✅ **智能去重算法**: 平衡数据量和信息完整性
- ✅ **多模型融合**: 结合传统ML和深度学习优势
- ✅ **预测模式优化**: 专门处理故障预测任务

## 🤝 贡献指南

### 文档更新：
1. 在相应文档中添加新内容
2. 更新本README中的索引
3. 确保文档格式统一

### 代码更新：
1. 保持代码注释完整
2. 更新相关文档说明
3. 添加必要的实验记录

## 📞 联系方式

如有问题或建议，请参考各文档中的说明或查看代码注释。

---

**最后更新**: 2025年1月12日
**项目版本**: v1.0.0
