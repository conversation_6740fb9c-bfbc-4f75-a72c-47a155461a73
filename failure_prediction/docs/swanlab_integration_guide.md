# SwanLab集成和配置管理指南

## 🎯 新功能概述

### 1. 时间去重可配置 ✅
- **配置参数**: `enable_time_deduplication`
- **默认值**: `False` (数据已预处理，无需去重)
- **用途**: 控制是否在数据处理过程中去除相邻重复时间戳

### 2. SwanLab完整配置集成 ✅
- **自动记录**: 所有配置参数自动记录到SwanLab
- **分类管理**: 配置按模型、训练、数据、损失分类
- **实验对比**: 不同配置的实验可以轻松对比

## 📋 配置参数详解

### 时间去重配置
```python
config = FailurePredictionConfig()

# 关闭时间去重（默认，推荐）
config.enable_time_deduplication = False
# 适用于: 数据已经预处理去重
# 效果: 直接使用过滤后特征，提高处理效率

# 启用时间去重
config.enable_time_deduplication = True  
# 适用于: 原始数据可能有重复时间戳
# 效果: 处理过程中去除相邻重复时间戳
```

### SwanLab配置分类

#### 🏗️ 模型信息 (7项)
- `model_architecture`: 模型类型 (lstm/gru/transformer/mlp)
- `hidden_dim`: 隐藏层维度
- `num_layers`: 网络层数
- `bidirectional`: 是否双向 (RNN)
- `dropout_rate`: Dropout比例
- `feature_dim`: 特征维度
- `max_seq_length`: 最大序列长度

#### 🎓 训练信息 (8项)
- `batch_size`: 批大小
- `learning_rate`: 学习率
- `max_epochs`: 最大训练轮数
- `patience`: 早停耐心值
- `weight_decay`: 权重衰减
- `optimizer`: 优化器类型
- `lr_scheduler`: 学习率调度器
- `lr_warmup_epochs`: 预热轮数

#### 📊 数据信息 (8项)
- `train_size`: 训练集比例
- `val_size`: 验证集比例
- `test_size`: 测试集比例
- `oversampling`: 是否过采样
- `oversampling_method`: 过采样方法
- `oversampling_ratio`: 过采样比例
- `time_deduplication`: 时间去重设置
- `filter_tp_before_hours`: TP前过滤时间

#### 📉 损失信息 (5项)
- `loss_type`: 损失函数类型
- `focal_alpha`: Focal Loss α参数
- `focal_gamma`: Focal Loss γ参数
- `label_smoothing`: 标签平滑参数
- `pos_weight_scale`: 正样本权重倍数

## 🚀 使用方式

### 基本使用（自动SwanLab集成）
```python
from failure_prediction.model import train_failure_prediction_model

# 直接训练，SwanLab自动记录所有配置
model, trainer = train_failure_prediction_model()
```

### 自定义配置
```python
from failure_prediction.model import FailurePredictionConfig

config = FailurePredictionConfig()

# 模型配置
config.model_type = "transformer"
config.hidden_dim = 512
config.num_layers = 6

# 数据配置
config.enable_time_deduplication = True  # 启用时间去重
config.use_oversampling = True
config.oversampling_ratio = 0.25

# 损失配置
config.use_focal_loss = True
config.focal_alpha = 0.3
config.focal_gamma = 2.5

# 训练配置
config.learning_rate = 0.0005
config.use_lr_scheduler = True
config.lr_scheduler_type = "cosine"

# 开始训练（配置会自动记录到SwanLab）
# model, trainer = train_failure_prediction_model_with_config(config)
```

### 配置管理方法
```python
config = FailurePredictionConfig()

# 转换为字典
config_dict = config.to_dict()

# 从字典更新
config.update_from_dict({"model_type": "gru", "hidden_dim": 256})

# 获取分类信息
model_info = config.get_model_info()
training_info = config.get_training_info()
data_info = config.get_data_info()
loss_info = config.get_loss_info()
```

## 📊 SwanLab实验管理

### 实验命名规则
```
实验名称格式: {model_type}-{hidden_dim}d-{num_layers}l
示例:
- lstm-256d-3l
- transformer-512d-6l  
- gru-128d-2l
```

### 自动记录内容
1. **启动时记录**:
   - 完整配置参数 (30+ 项)
   - 模型架构信息
   - 数据处理设置
   - 训练超参数

2. **训练过程记录**:
   - 训练/验证损失
   - 各种评估指标
   - 学习率变化
   - 模型参数统计

3. **训练完成记录**:
   - 最终模型性能
   - 混淆矩阵
   - 测试集结果

### 配置对比示例
| 实验名称 | 模型 | 隐藏维度 | 损失函数 | 时间去重 | F1分数 |
|----------|------|----------|----------|----------|--------|
| lstm-128d-2l | LSTM | 128 | Focal | False | 0.85 |
| gru-256d-3l | GRU | 256 | LabelSmooth | True | 0.87 |
| transformer-512d-6l | Transformer | 512 | Focal | False | 0.89 |

## 🔧 配置建议

### 针对当前数据集
```python
# 推荐配置1: LSTM + Focal Loss
config.model_type = "lstm"
config.hidden_dim = 256
config.num_layers = 3
config.enable_time_deduplication = False  # 数据已预处理
config.use_focal_loss = True
config.use_oversampling = True
config.oversampling_ratio = 0.3
```

### 针对不同场景
```python
# 场景1: 数据已预处理，追求效率
config.enable_time_deduplication = False
config.model_type = "lstm"

# 场景2: 原始数据，需要去重
config.enable_time_deduplication = True
config.model_type = "transformer"

# 场景3: 严重不平衡数据
config.use_focal_loss = True
config.focal_gamma = 3.0
config.use_oversampling = True
```

## 📈 监控和分析

### SwanLab Web界面功能
1. **实验对比**: 并排对比不同配置的结果
2. **参数分析**: 分析哪些参数对性能影响最大
3. **趋势追踪**: 观察训练过程中的指标变化
4. **配置搜索**: 根据配置参数筛选实验

### 关键监控指标
- **val_f1**: 验证集F1分数（主要优化目标）
- **val_auc**: 验证集AUC（判别能力）
- **val_recall**: 验证集召回率（故障检测能力）
- **train_loss vs val_loss**: 过拟合检测

## 🎯 最佳实践

1. **配置管理**:
   - 使用描述性的实验名称
   - 记录配置变更的原因
   - 保持配置的版本控制

2. **实验设计**:
   - 每次只改变少数几个参数
   - 使用相同的随机种子确保可重现
   - 记录实验的假设和预期

3. **结果分析**:
   - 关注验证集而非训练集指标
   - 分析失败案例和边界情况
   - 考虑模型的实际部署需求

## 🚀 下一步

1. **开始实验**: 使用不同配置进行多组实验
2. **分析结果**: 在SwanLab中对比实验结果
3. **优化配置**: 基于结果调整超参数
4. **模型选择**: 选择最佳配置进行最终训练

通过这些改进，您现在可以：
- ✅ 灵活控制时间去重行为
- ✅ 自动记录完整的实验配置
- ✅ 在SwanLab中轻松对比不同实验
- ✅ 更好地管理和追踪模型训练过程
