#!/usr/bin/env python3
"""
测试SwanLab集成和配置功能
验证配置字典转换和时间去重配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_dict_conversion():
    """测试配置字典转换功能"""
    print("🧪 测试配置字典转换...")
    
    try:
        from failure_prediction.model import FailurePredictionConfig
        
        config = FailurePredictionConfig()
        
        # 测试转换为字典
        config_dict = config.to_dict()
        
        print(f"✅ 配置转换为字典成功")
        print(f"   - 配置项数量: {len(config_dict)}")
        
        # 显示部分配置
        key_configs = [
            "model_type", "hidden_dim", "num_layers", "batch_size", 
            "learning_rate", "use_oversampling", "enable_time_deduplication"
        ]
        
        for key in key_configs:
            if key in config_dict:
                print(f"   - {key}: {config_dict[key]}")
        
        # 测试从字典更新配置
        test_updates = {
            "model_type": "transformer",
            "hidden_dim": 512,
            "enable_time_deduplication": True
        }
        
        config.update_from_dict(test_updates)
        
        print(f"✅ 从字典更新配置成功")
        for key, value in test_updates.items():
            actual_value = getattr(config, key)
            print(f"   - {key}: {actual_value} (预期: {value})")
            assert actual_value == value, f"配置更新失败: {key}"
        
        return True
        
    except Exception as e:
        print(f"❌ 配置字典转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_info_methods():
    """测试配置信息获取方法"""
    print("\n🧪 测试配置信息获取方法...")
    
    try:
        from failure_prediction.model import FailurePredictionConfig
        
        config = FailurePredictionConfig()
        config.model_type = "lstm"
        config.use_oversampling = True
        config.use_focal_loss = True
        
        # 测试各种信息获取方法
        model_info = config.get_model_info()
        training_info = config.get_training_info()
        data_info = config.get_data_info()
        loss_info = config.get_loss_info()
        
        print(f"✅ 配置信息获取成功")
        print(f"   - 模型信息: {len(model_info)} 项")
        print(f"   - 训练信息: {len(training_info)} 项")
        print(f"   - 数据信息: {len(data_info)} 项")
        print(f"   - 损失信息: {len(loss_info)} 项")
        
        # 验证关键信息
        assert model_info["model_architecture"] == "lstm"
        assert data_info["oversampling"] == True
        assert loss_info["loss_type"] == "focal"
        
        print(f"   - 模型架构: {model_info['model_architecture']}")
        print(f"   - 过采样: {data_info['oversampling']}")
        print(f"   - 损失类型: {loss_info['loss_type']}")
        print(f"   - 时间去重: {data_info['time_deduplication']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置信息获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_swanlab_config_preparation():
    """测试SwanLab配置准备"""
    print("\n🧪 测试SwanLab配置准备...")
    
    try:
        from failure_prediction.model import FailurePredictionConfig
        
        config = FailurePredictionConfig()
        config.model_type = "gru"
        config.hidden_dim = 256
        config.use_oversampling = True
        config.enable_time_deduplication = False
        
        # 准备SwanLab配置
        swanlab_config = {
            **config.get_model_info(),
            **config.get_training_info(), 
            **config.get_data_info(),
            **config.get_loss_info(),
            "random_seed": config.random_seed,
            "accelerator": config.accelerator,
        }
        
        print(f"✅ SwanLab配置准备成功")
        print(f"   - 总配置项: {len(swanlab_config)}")
        
        # 验证关键配置
        expected_keys = [
            "model_architecture", "hidden_dim", "batch_size", "learning_rate",
            "oversampling", "time_deduplication", "loss_type", "random_seed"
        ]
        
        for key in expected_keys:
            assert key in swanlab_config, f"缺少关键配置: {key}"
            print(f"   - {key}: {swanlab_config[key]}")
        
        return True
        
    except Exception as e:
        print(f"❌ SwanLab配置准备测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_deduplication_config():
    """测试时间去重配置"""
    print("\n🧪 测试时间去重配置...")
    
    try:
        from failure_prediction.model import FailurePredictionConfig
        
        # 测试默认配置（去重关闭）
        config1 = FailurePredictionConfig()
        assert config1.enable_time_deduplication == False, "默认应该关闭时间去重"
        
        # 测试启用去重
        config2 = FailurePredictionConfig()
        config2.enable_time_deduplication = True
        assert config2.enable_time_deduplication == True, "应该能够启用时间去重"
        
        print(f"✅ 时间去重配置测试通过")
        print(f"   - 默认状态: {config1.enable_time_deduplication}")
        print(f"   - 启用状态: {config2.enable_time_deduplication}")
        
        # 验证配置在字典中正确反映
        data_info1 = config1.get_data_info()
        data_info2 = config2.get_data_info()
        
        assert data_info1["time_deduplication"] == False
        assert data_info2["time_deduplication"] == True
        
        print(f"   - 字典中反映正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间去重配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_swanlab_availability():
    """测试SwanLab可用性"""
    print("\n🧪 测试SwanLab可用性...")
    
    try:
        from failure_prediction.model import SWANLAB_AVAILABLE
        
        print(f"✅ SwanLab可用性检查完成")
        print(f"   - SwanLab可用: {SWANLAB_AVAILABLE}")
        
        if SWANLAB_AVAILABLE:
            import swanlab
            print(f"   - SwanLab版本: {swanlab.__version__}")
        else:
            print(f"   - 将使用本地日志记录")
        
        return True
        
    except Exception as e:
        print(f"❌ SwanLab可用性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 开始测试SwanLab集成和配置功能")
    print("=" * 60)
    
    tests = [
        test_config_dict_conversion,
        test_config_info_methods,
        test_swanlab_config_preparation,
        test_time_deduplication_config,
        test_swanlab_availability,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！SwanLab集成准备就绪！")
        print("\n📈 主要功能:")
        print("   ✅ 配置字典转换和更新")
        print("   ✅ 分类配置信息获取")
        print("   ✅ SwanLab配置准备")
        print("   ✅ 时间去重可配置")
        print("   ✅ SwanLab可用性检查")
        
        print("\n🚀 使用示例:")
        print("   # 启用时间去重")
        print("   config.enable_time_deduplication = True")
        print("   ")
        print("   # SwanLab将自动记录完整配置")
        print("   # 包括模型、训练、数据、损失等所有参数")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
