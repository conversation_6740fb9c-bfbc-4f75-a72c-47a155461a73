#!/usr/bin/env python3
"""
测试增强的故障预测模型
验证LSTM、GRU、Transformer架构和各种扩展方法
"""

import sys
import os
import torch
import numpy as np
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_model_architectures():
    """测试不同的模型架构"""
    print("🧪 测试不同模型架构...")
    
    from failure_prediction.model import FailurePredictionConfig, FailurePredictionModel
    
    architectures = ["mlp", "lstm", "gru", "transformer"]
    
    for arch in architectures:
        try:
            print(f"\n📐 测试 {arch.upper()} 架构...")
            
            config = FailurePredictionConfig()
            config.model_type = arch
            
            # 调整Transformer参数
            if arch == "transformer":
                config.num_heads = 4  # 减少头数以适应feature_dim=16
                config.feature_dim = 16  # 确保能被num_heads整除
            
            model = FailurePredictionModel(config)
            
            # 创建测试数据
            batch_size = 8
            seq_len = 20
            sequences = torch.randn(batch_size, seq_len, config.feature_dim)
            time_diffs = torch.randn(batch_size, seq_len)
            lengths = torch.randint(5, seq_len, (batch_size,))
            
            # 前向传播
            with torch.no_grad():
                outputs = model(sequences, time_diffs, lengths)
            
            print(f"✅ {arch.upper()} 架构测试通过")
            print(f"   - 输出形状: {outputs.shape}")
            print(f"   - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
        except Exception as e:
            print(f"❌ {arch.upper()} 架构测试失败: {e}")
            return False
    
    return True

def test_loss_functions():
    """测试不同的损失函数"""
    print("\n🧪 测试不同损失函数...")
    
    from failure_prediction.model import (
        FailurePredictionConfig, 
        FailurePredictionModel,
        FocalLoss,
        LabelSmoothingBCELoss
    )
    
    loss_configs = [
        {"use_focal_loss": True, "use_label_smoothing": False, "use_class_weights": False},
        {"use_focal_loss": False, "use_label_smoothing": True, "use_class_weights": False},
        {"use_focal_loss": False, "use_label_smoothing": False, "use_class_weights": True},
    ]
    
    for i, loss_config in enumerate(loss_configs):
        try:
            config = FailurePredictionConfig()
            config.model_type = "lstm"
            
            # 应用损失函数配置
            for key, value in loss_config.items():
                setattr(config, key, value)
            
            model = FailurePredictionModel(config)
            
            # 测试损失计算
            batch_size = 8
            outputs = torch.randn(batch_size)
            labels = torch.randint(0, 2, (batch_size,)).float()
            
            loss = model.criterion(outputs, labels)
            
            loss_name = "Focal" if loss_config["use_focal_loss"] else \
                       "LabelSmoothing" if loss_config["use_label_smoothing"] else \
                       "WeightedBCE" if loss_config["use_class_weights"] else "BCE"
            
            print(f"✅ {loss_name} 损失函数测试通过，损失值: {loss.item():.4f}")
            
        except Exception as e:
            print(f"❌ 损失函数 {i+1} 测试失败: {e}")
            return False
    
    return True

def test_oversampling():
    """测试过采样功能"""
    print("\n🧪 测试过采样功能...")
    
    try:
        from failure_prediction.model import FailurePredictionConfig, FailurePredictionDataset
        
        # 创建不平衡的模拟数据
        mock_data = []
        
        # 创建90个负样本
        for i in range(90):
            mock_data.append({
                "feature_list": [
                    [f"2024-01-01 10:{i:02d}:00"] + [np.random.randn() for _ in range(16)]
                    for _ in range(10)
                ],
                "group_key": ["group", "key", "test", "2024-01-01 12:00:00"],
                "label_list": [5]  # 负样本
            })
        
        # 创建10个正样本
        for i in range(10):
            mock_data.append({
                "feature_list": [
                    [f"2024-01-01 10:{i:02d}:00"] + [np.random.randn() for _ in range(16)]
                    for _ in range(10)
                ],
                "group_key": ["group", "key", "test", "2024-01-01 12:00:00"],
                "label_list": [2]  # 正样本
            })
        
        config = FailurePredictionConfig()
        config.use_oversampling = True
        config.oversampling_ratio = 0.3
        
        dataset = FailurePredictionDataset(mock_data, config)
        
        # 统计类别分布
        unique, counts = np.unique(dataset.labels, return_counts=True)
        distribution = dict(zip(unique, counts))
        
        print(f"✅ 过采样测试通过")
        print(f"   - 最终类别分布: {distribution}")
        print(f"   - 数据集大小: {len(dataset)}")
        
        # 验证正样本比例
        pos_ratio = distribution.get(1, 0) / len(dataset)
        print(f"   - 正样本比例: {pos_ratio:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 过采样测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step():
    """测试训练步骤"""
    print("\n🧪 测试训练步骤...")
    
    try:
        from failure_prediction.model import (
            FailurePredictionConfig, 
            FailurePredictionModel,
            collate_fn
        )
        
        config = FailurePredictionConfig()
        config.model_type = "lstm"
        config.use_focal_loss = True
        
        model = FailurePredictionModel(config)
        
        # 创建模拟批次
        batch_size = 8
        seq_len = 15
        
        batch_items = []
        for i in range(batch_size):
            item = {
                "sequence": torch.randn(seq_len, config.feature_dim),
                "time_diffs": torch.randn(seq_len),
                "label": torch.tensor(i % 2, dtype=torch.float),
                "length": torch.tensor([seq_len])
            }
            batch_items.append(item)
        
        batch = collate_fn(batch_items)
        
        # 测试训练步骤
        model.train()
        loss = model.training_step(batch, 0)
        
        print(f"✅ 训练步骤测试通过")
        print(f"   - 训练损失: {loss.item():.4f}")
        
        # 测试验证步骤
        model.eval()
        with torch.no_grad():
            val_loss = model.validation_step(batch, 0)
        
        print(f"   - 验证损失: {val_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 开始测试增强的故障预测模型")
    print("=" * 60)
    
    tests = [
        test_model_architectures,
        test_loss_functions,
        test_oversampling,
        test_training_step,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强模型准备就绪！")
        print("\n📈 主要改进:")
        print("   ✅ 支持多种序列架构 (MLP/LSTM/GRU/Transformer)")
        print("   ✅ 实现过采样平衡数据集")
        print("   ✅ 添加Focal Loss和标签平滑")
        print("   ✅ 增强的分类器架构")
        print("   ✅ 学习率调度和优化器改进")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
